import { UserRole } from "@/lib/auth-config"
import {
  getOrganizationById,
  getOrganizationMembers,
  updateOrganization,
  verifyOrganization
} from "@/lib/db"
import { getServerSession } from "@/lib/session"
import { NextRequest, NextResponse } from "next/server"
import { z } from "zod"

// Validation schema for organization updates
const updateOrganizationSchema = z.object({
  name: z.string().min(1).optional(),
  type: z.enum(["yacht_company", "cert_provider"]).optional(),
  contactEmail: z.string().email().optional(),
  description: z.string().nullable().optional(),
  website: z.string().nullable().optional(),
})

/**
 * Get individual organization details
 */
export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession()

    // Check authentication and authorization
    if (!session || session.role !== UserRole.SYSTEM_ADMIN) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    const { id } = await params

    // Get organization details
    const organization = await getOrganizationById(id)
    if (!organization) {
      return NextResponse.json(
        { error: "Organization not found" },
        { status: 404 }
      )
    }

    // Get organization members
    const members = await getOrganizationMembers(id)

    return NextResponse.json({
      success: true,
      organization: {
        ...organization,
        memberCount: members.length,
        members
      }
    })

  } catch (error) {
    console.error("Error fetching organization:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

/**
 * Update organization details
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession()

    // Check authentication and authorization
    if (!session || session.role !== UserRole.SYSTEM_ADMIN) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    const { id } = await params
    const body = await request.json()
    const validatedData = updateOrganizationSchema.parse(body)

    // Verify the organization exists
    const organization = await getOrganizationById(id)
    if (!organization) {
      return NextResponse.json(
        { error: "Organization not found" },
        { status: 404 }
      )
    }

    // Update the organization
    await updateOrganization(id, validatedData)

    // Get updated organization
    const updatedOrganization = await getOrganizationById(id)

    return NextResponse.json({
      success: true,
      message: "Organization updated successfully",
      organization: updatedOrganization
    })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: "Validation error",
          details: error.errors
        },
        { status: 400 }
      )
    }

    console.error("Error updating organization:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

/**
 * Organization actions (verify, suspend, reactivate)
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession()

    // Check authentication and authorization
    if (!session || session.role !== UserRole.SYSTEM_ADMIN) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    const { id } = await params
    const body = await request.json()
    const { action } = body

    // Verify the organization exists
    const organization = await getOrganizationById(id)
    if (!organization) {
      return NextResponse.json(
        { error: "Organization not found" },
        { status: 404 }
      )
    }

    switch (action) {
      case "verify": {
        if (organization.status === "verified") {
          return NextResponse.json(
            { error: "Organization is already verified" },
            { status: 400 }
          )
        }

        await verifyOrganization(id, session.userId)

        return NextResponse.json({
          success: true,
          message: "Organization verified successfully"
        })
      }

      case "suspend": {
        if (organization.status === "suspended") {
          return NextResponse.json(
            { error: "Organization is already suspended" },
            { status: 400 }
          )
        }

        // Import suspend function dynamically to avoid import issues
        const { suspendOrganization } = await import("@/lib/db")
        await suspendOrganization(id)

        return NextResponse.json({
          success: true,
          message: "Organization suspended successfully"
        })
      }

      case "reactivate": {
        if (organization.status !== "suspended") {
          return NextResponse.json(
            { error: "Organization is not suspended" },
            { status: 400 }
          )
        }

        // Import reactivate function dynamically to avoid import issues
        const { reactivateOrganization } = await import("@/lib/db")
        await reactivateOrganization(id)

        return NextResponse.json({
          success: true,
          message: "Organization reactivated successfully"
        })
      }

      default:
        return NextResponse.json(
          { error: "Invalid action" },
          { status: 400 }
        )
    }

  } catch (error) {
    console.error("Error processing organization action:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
