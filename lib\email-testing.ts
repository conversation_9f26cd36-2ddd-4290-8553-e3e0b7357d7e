import { sendEmailWithRetry } from "@/lib/email-retry";
import { logger } from "@/lib/logger";

// Email testing configuration
export interface EmailTestConfig {
  testEmail: string;
  environment: 'development' | 'staging' | 'production';
  skipActualSend?: boolean; // For testing without sending real emails
}

// Email test result
export interface EmailTestResult {
  success: boolean;
  testType: string;
  duration: number;
  error?: string;
  details?: any;
}

// Email delivery test suite
export class EmailDeliveryTester {
  private config: EmailTestConfig;

  constructor(config: EmailTestConfig) {
    this.config = config;
  }

  // Test basic email sending
  async testBasicDelivery(): Promise<EmailTestResult> {
    const startTime = Date.now();

    try {
      logger.info("email", "Testing basic email delivery", {
        testEmail: this.config.testEmail,
        environment: this.config.environment,
      });

      if (this.config.skipActualSend) {
        // Simulate email sending for testing
        await new Promise(resolve => setTimeout(resolve, 100));

        return {
          success: true,
          testType: "basic_delivery",
          duration: Date.now() - startTime,
          details: { simulated: true },
        };
      }

      // Import email function dynamically
      const { sendEmail } = await import("@/lib/email");

      const result = await sendEmailWithRetry(
        () => sendEmail({
          to: this.config.testEmail,
          subject: "Sailor Plus Email Delivery Test",
          html: `
            <h2>Email Delivery Test</h2>
            <p>This is a test email to verify email delivery is working correctly.</p>
            <p><strong>Environment:</strong> ${this.config.environment}</p>
            <p><strong>Timestamp:</strong> ${new Date().toISOString()}</p>
            <p>If you received this email, the delivery system is working properly.</p>
          `,
          text: `
            Email Delivery Test
            
            This is a test email to verify email delivery is working correctly.
            Environment: ${this.config.environment}
            Timestamp: ${new Date().toISOString()}
            
            If you received this email, the delivery system is working properly.
          `,
        }),
        "email-delivery-test"
      );

      return {
        success: result.success,
        testType: "basic_delivery",
        duration: Date.now() - startTime,
        error: result.error,
        details: { emailSent: result.success },
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);

      logger.error("email", "Basic email delivery test failed", {
        error: errorMessage,
        testEmail: this.config.testEmail,
      });

      return {
        success: false,
        testType: "basic_delivery",
        duration: Date.now() - startTime,
        error: errorMessage,
      };
    }
  }

  // Test email templates
  async testEmailTemplates(): Promise<EmailTestResult> {
    const startTime = Date.now();

    try {
      logger.info("email", "Testing email templates", {
        testEmail: this.config.testEmail,
      });

      if (this.config.skipActualSend) {
        return {
          success: true,
          testType: "template_test",
          duration: Date.now() - startTime,
          details: { simulated: true, templatesChecked: 4 },
        };
      }

      // Import email functions
      const {
        sendVerificationEmail,
        sendPasswordResetEmail,
        sendAccountDeletionEmail,
        sendWelcomeEmail
      } = await import("@/lib/email");

      const templateTests = [
        {
          name: "verification",
          test: () => sendVerificationEmail(
            this.config.testEmail,
            "Test User",
            "test-token-123"
          ),
        },
        {
          name: "password_reset",
          test: () => sendPasswordResetEmail(
            this.config.testEmail,
            "Test User",
            "reset-token-123"
          ),
        },
        {
          name: "welcome",
          test: () => sendWelcomeEmail(
            this.config.testEmail,
            "Test User",
            "test"
          ),
        },
        {
          name: "account_deletion",
          test: () => sendAccountDeletionEmail(
            this.config.testEmail,
            "Test User",
            {
              type: "scheduled",
              deletionDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
              recoveryToken: "recovery-token-123",
            }
          ),
        },
      ];

      const results = [];

      for (const template of templateTests) {
        try {
          const result = await template.test();
          results.push({
            template: template.name,
            success: result.success,
            error: result.error,
          });
        } catch (error) {
          results.push({
            template: template.name,
            success: false,
            error: error instanceof Error ? error.message : String(error),
          });
        }
      }

      const successCount = results.filter(r => r.success).length;
      const allSuccessful = successCount === results.length;

      return {
        success: allSuccessful,
        testType: "template_test",
        duration: Date.now() - startTime,
        details: {
          totalTemplates: results.length,
          successfulTemplates: successCount,
          results,
        },
        ...((!allSuccessful) && {
          error: `${results.length - successCount} template(s) failed`,
        }),
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);

      logger.error("email", "Email template test failed", {
        error: errorMessage,
        testEmail: this.config.testEmail,
      });

      return {
        success: false,
        testType: "template_test",
        duration: Date.now() - startTime,
        error: errorMessage,
      };
    }
  }

  // Test email retry logic
  async testRetryLogic(): Promise<EmailTestResult> {
    const startTime = Date.now();

    try {
      logger.info("email", "Testing email retry logic");

      // Simulate a failing email function that succeeds on the 2nd attempt
      let attempts = 0;
      const mockEmailFunction = () => {
        attempts++;
        if (attempts < 2) {
          throw new Error("Simulated email failure");
        }
        return Promise.resolve({ success: true });
      };

      const result = await sendEmailWithRetry(mockEmailFunction, "retry-test");

      return {
        success: result.success && attempts === 2,
        testType: "retry_logic",
        duration: Date.now() - startTime,
        details: {
          attempts,
          expectedAttempts: 2,
          retryWorked: attempts === 2,
        },
        ...((!result.success) && { error: result.error }),
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);

      logger.error("email", "Email retry logic test failed", {
        error: errorMessage,
      });

      return {
        success: false,
        testType: "retry_logic",
        duration: Date.now() - startTime,
        error: errorMessage,
      };
    }
  }

  // Run all email tests
  async runAllTests(): Promise<{
    success: boolean;
    results: EmailTestResult[];
    summary: {
      total: number;
      passed: number;
      failed: number;
      totalDuration: number;
    };
  }> {
    const startTime = Date.now();

    logger.info("email", "Running comprehensive email test suite", {
      testEmail: this.config.testEmail,
      environment: this.config.environment,
    });

    const tests = [
      () => this.testBasicDelivery(),
      () => this.testRetryLogic(),
      () => this.testEmailTemplates(),
    ];

    const results: EmailTestResult[] = [];

    for (const test of tests) {
      try {
        const result = await test();
        results.push(result);
      } catch (error) {
        results.push({
          success: false,
          testType: "unknown",
          duration: 0,
          error: error instanceof Error ? error.message : String(error),
        });
      }
    }

    const passed = results.filter(r => r.success).length;
    const failed = results.length - passed;
    const totalDuration = Date.now() - startTime;
    const allPassed = failed === 0;

    const summary = {
      total: results.length,
      passed,
      failed,
      totalDuration,
    };

    logger.info("email", "Email test suite completed", {
      summary,
      allPassed,
    });

    return {
      success: allPassed,
      results,
      summary,
    };
  }
}

// Email performance monitoring
export class EmailPerformanceMonitor {
  private metrics: Array<{
    timestamp: Date;
    type: string;
    duration: number;
    success: boolean;
    retries: number;
  }> = [];

  recordEmailSend(type: string, duration: number, success: boolean, retries = 0) {
    this.metrics.push({
      timestamp: new Date(),
      type,
      duration,
      success,
      retries,
    });

    // Keep only last 1000 metrics
    if (this.metrics.length > 1000) {
      this.metrics = this.metrics.slice(-1000);
    }

    // Log slow emails
    if (duration > 5000) { // 5 seconds
      logger.warn("email", "Slow email delivery detected", {
        type,
        duration,
        success,
        retries,
      });
    }
  }

  getMetrics(hours = 24) {
    const cutoff = new Date(Date.now() - hours * 60 * 60 * 1000);
    const recentMetrics = this.metrics.filter(m => m.timestamp > cutoff);

    if (recentMetrics.length === 0) {
      return {
        totalEmails: 0,
        successRate: 0,
        averageDuration: 0,
        retryRate: 0,
      };
    }

    const successful = recentMetrics.filter(m => m.success).length;
    const withRetries = recentMetrics.filter(m => m.retries > 0).length;
    const totalDuration = recentMetrics.reduce((sum, m) => sum + m.duration, 0);

    return {
      totalEmails: recentMetrics.length,
      successRate: Math.round((successful / recentMetrics.length) * 100),
      averageDuration: Math.round(totalDuration / recentMetrics.length),
      retryRate: Math.round((withRetries / recentMetrics.length) * 100),
      recentMetrics: recentMetrics.slice(-10), // Last 10 emails
    };
  }
}

// Global email performance monitor
export const emailPerformanceMonitor = new EmailPerformanceMonitor();
