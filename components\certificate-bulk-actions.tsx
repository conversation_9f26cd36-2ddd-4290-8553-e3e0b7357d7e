"use client";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import type { Certificate } from "@/hooks/use-certificates";
import { useToast } from "@/hooks/use-toast";
import {
  CheckSquare,
  Download,
  MoreHorizontal,
  Square,
  Star,
  StarOff,
  Trash2,
} from "lucide-react";
import { useState } from "react";

interface CertificateBulkActionsProps {
  selectedCertificates: Certificate[];
  onSelectAll: () => void;
  onDeselectAll: () => void;
  onBulkDelete: (ids: string[]) => Promise<void>;
  onBulkFavorite: (ids: string[], isFavorite: boolean) => Promise<void>;
  onBulkDownload: (ids: string[]) => Promise<void>;
  totalCertificates: number;
  className?: string;
}

export function CertificateBulkActions({
  selectedCertificates,
  onSelectAll,
  onDeselectAll,
  onBulkDelete,
  onBulkFavorite,
  onBulkDownload,
  totalCertificates,
  className,
}: CertificateBulkActionsProps) {
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const selectedCount = selectedCertificates.length;
  const isAllSelected =
    selectedCount === totalCertificates && totalCertificates > 0;
  // const isPartiallySelected =
  //   selectedCount > 0 && selectedCount < totalCertificates;

  const handleBulkAction = async (
    action: () => Promise<void>,
    successMessage: string,
    errorMessage: string
  ) => {
    if (selectedCount === 0) return;

    setIsLoading(true);
    try {
      await action();
      toast({
        title: "Success",
        description: successMessage,
        "data-testid": "bulk-success-toast",
      } as any);
    } catch (error) {
      console.error("Bulk action error:", error);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = () => {
    const ids = selectedCertificates.map((cert) => cert.id);
    handleBulkAction(
      () => onBulkDelete(ids),
      `Successfully deleted ${selectedCount} certificate${
        selectedCount > 1 ? "s" : ""
      }`,
      "Failed to delete certificates"
    );
    setIsDeleteDialogOpen(false);
  };

  const handleFavorite = (isFavorite: boolean) => {
    const ids = selectedCertificates.map((cert) => cert.id);
    handleBulkAction(
      () => onBulkFavorite(ids, isFavorite),
      `Successfully ${isFavorite ? "added to" : "removed from"} favorites`,
      "Failed to update favorites"
    );
  };

  const handleDownload = () => {
    const ids = selectedCertificates.map((cert) => cert.id);
    handleBulkAction(
      () => onBulkDownload(ids),
      `Downloading ${selectedCount} certificate${
        selectedCount > 1 ? "s" : ""
      }...`,
      "Failed to download certificates"
    );
  };

  if (selectedCount === 0) {
    return (
      <div className="flex items-center gap-2">
        <Button
          variant="outline"
          size="sm"
          onClick={isAllSelected ? onDeselectAll : onSelectAll}
          className="gap-2"
          data-testid="select-all-button"
        >
          {isAllSelected ? (
            <>
              <CheckSquare className="h-4 w-4" />
              Deselect All
            </>
          ) : (
            <>
              <Square className="h-4 w-4" />
              Select All
            </>
          )}
        </Button>
      </div>
    );
  }

  return (
    <>
      <div
        className={`flex items-center gap-2 p-3 bg-primary/5 border rounded-lg ${className}`}
      >
        {/* Selection Info */}
        <div className="flex items-center gap-2">
          <Badge variant="secondary" className="gap-1">
            <CheckSquare className="h-3 w-3" />
            {selectedCount} selected
          </Badge>

          <Button
            variant="ghost"
            size="sm"
            onClick={onDeselectAll}
            className="h-auto p-1 text-muted-foreground hover:text-foreground"
            data-testid="deselect-all-button"
          >
            Clear
          </Button>
        </div>

        {/* Quick Actions */}
        <div className="flex items-center gap-1 ml-auto">
          {/* Download */}
          <Button
            variant="outline"
            size="sm"
            onClick={handleDownload}
            disabled={isLoading}
            className="gap-2"
            data-testid="bulk-download-button"
          >
            <Download className="h-4 w-4" />
            <span className="hidden sm:inline">Download</span>
          </Button>

          {/* Favorite/Unfavorite */}
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleFavorite(true)}
            disabled={isLoading}
            className="gap-2"
            data-testid="bulk-favorite-button"
          >
            <Star className="h-4 w-4" />
            <span className="hidden sm:inline">Favorite</span>
          </Button>

          {/* More Actions */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="outline"
                size="sm"
                disabled={isLoading}
                data-testid="bulk-actions-menu"
              >
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              <DropdownMenuItem onClick={() => handleFavorite(false)}>
                <StarOff className="h-4 w-4 mr-2" />
                Remove from Favorites
              </DropdownMenuItem>

              <DropdownMenuSeparator />

              <DropdownMenuItem
                onClick={() => setIsDeleteDialogOpen(true)}
                className="text-destructive focus:text-destructive"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete Selected
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Delete Confirmation Dialog */}
      <AlertDialog
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Certificates</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete {selectedCount} certificate
              {selectedCount > 1 ? "s" : ""}? This action cannot be undone and
              will permanently remove the certificate
              {selectedCount > 1 ? "s" : ""}
              and all associated files.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete {selectedCount} Certificate{selectedCount > 1 ? "s" : ""}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
