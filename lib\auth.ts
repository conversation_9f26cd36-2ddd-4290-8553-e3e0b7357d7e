import { IndividualSubscriptionPlan, UserRole } from "@/lib/auth-config"
import { createUser, getUserByEmail, getUserBySocialAccount, linkSocialAccount } from "@/lib/db"
import { compare } from "bcrypt"
import { nanoid } from "nanoid"
import type { NextAuthOptions } from "next-auth"
import CredentialsProvider from "next-auth/providers/credentials"
import FacebookProvider from "next-auth/providers/facebook"
import GoogleProvider from "next-auth/providers/google"

// NextAuth types are extended in types/next-auth.d.ts

export const authOptions: NextAuthOptions = {
  session: {
    strategy: "jwt",
  },
  pages: {
    signIn: "/login",
    signOut: "/login",
    error: "/login",
  },
  debug: process.env.NODE_ENV === "development",
  providers: [
    // Google OAuth - Always available when configured
    ...(process.env.GOOGLE_CLIENT_ID && process.env.GOOGLE_CLIENT_SECRET ? [
      GoogleProvider({
        clientId: process.env.GOOGLE_CLIENT_ID!,
        clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
        authorization: {
          params: {
            prompt: "consent",
            access_type: "offline",
            response_type: "code"
          }
        }
      })
    ] : []),

    // Facebook OAuth - Conditionally enabled (requires business verification)
    ...(process.env.FACEBOOK_CLIENT_ID && process.env.FACEBOOK_CLIENT_SECRET ? [
      FacebookProvider({
        clientId: process.env.FACEBOOK_CLIENT_ID!,
        clientSecret: process.env.FACEBOOK_CLIENT_SECRET!,
      })
    ] : []),

    // Apple OAuth - Conditionally enabled (requires Apple Developer account)
    // Note: Apple provider requires special JWT configuration - disabled for now
    // ...(process.env.APPLE_ID && process.env.APPLE_TEAM_ID && process.env.APPLE_PRIVATE_KEY ? [
    //   AppleProvider({
    //     clientId: process.env.APPLE_ID!,
    //     clientSecret: process.env.APPLE_CLIENT_SECRET!, // This needs to be a JWT token
    //   })
    // ] : []),
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null
        }

        try {
          const user = await getUserByEmail(credentials.email)

          if (!user || !user.password) {
            return null
          }

          const isPasswordValid = await compare(credentials.password, user.password)

          if (!isPasswordValid) {
            return null
          }

          return {
            id: user.id,
            email: user.email,
            name: user.name,
            role: user.role,
            subscriptionPlan: user.subscriptionPlan,
            emailVerified: user.emailVerified || false,
          }
        } catch (error) {
          console.error("Auth error:", error)
          return null
        }
      },
    }),
  ],
  callbacks: {
    async redirect({ url, baseUrl }) {
      // Allows relative callback URLs
      if (url.startsWith("/")) return `${baseUrl}${url}`
      // Allows callback URLs on the same origin
      else if (new URL(url).origin === baseUrl) return url
      return `${baseUrl}/dashboard`
    },
    async signIn({ user, account, profile }) {
      if (account?.provider === "credentials") {
        return true
      }

      // Handle SSO sign-in
      if (account && profile && user.email) {
        try {
          // Check if user exists by email
          const existingUser = await getUserByEmail(user.email)

          if (existingUser) {
            // Check if this social account is already linked
            const socialAccount = await getUserBySocialAccount(account.provider, account.providerAccountId)

            if (!socialAccount) {
              // Link the social account to existing user
              await linkSocialAccount(existingUser.id, {
                provider: account.provider,
                providerAccountId: account.providerAccountId,
                accessToken: account.access_token,
                refreshToken: account.refresh_token,
                expiresAt: account.expires_at,
              })
            }

            // Hybrid Approach: If existing user is unverified, auto-verify them when linking OAuth
            if (!existingUser.emailVerified) {
              console.log(`Auto-verifying existing user ${existingUser.id} via ${account.provider} OAuth`)
              const { markEmailAsVerified } = await import("@/lib/db")
              await markEmailAsVerified(existingUser.id)

              // Send welcome email for newly verified user
              const { sendWelcomeEmail } = await import("@/lib/email")
              await sendWelcomeEmail(existingUser.email, existingUser.name, account.provider)
            }

            // Update user object with existing user data
            user.id = existingUser.id
            user.role = existingUser.role
            user.subscriptionPlan = existingUser.subscriptionPlan
          } else {
            // Create new user for SSO - Hybrid Approach: Auto-verify OAuth users
            const newUserId = nanoid()
            await createUser({
              id: newUserId,
              name: user.name || profile.name || "Unknown User",
              email: user.email,
              password: "", // No password for SSO users
              role: UserRole.INDIVIDUAL_USER,
              subscriptionPlan: IndividualSubscriptionPlan.FREE,
              emailVerified: true, // Hybrid Approach: OAuth emails are auto-verified
            })

            // Link the social account
            await linkSocialAccount(newUserId, {
              provider: account.provider,
              providerAccountId: account.providerAccountId,
              accessToken: account.access_token,
              refreshToken: account.refresh_token,
              expiresAt: account.expires_at,
            })

            // Send welcome email for new OAuth user
            console.log(`Sending welcome email to new ${account.provider} user: ${user.email}`)
            const { sendWelcomeEmail } = await import("@/lib/email")
            await sendWelcomeEmail(user.email, user.name || "User", account.provider)

            user.id = newUserId
            user.role = UserRole.INDIVIDUAL_USER
            user.subscriptionPlan = IndividualSubscriptionPlan.FREE
          }

          return true
        } catch (error) {
          console.error("SSO sign-in error:", error)
          return false
        }
      }

      return true
    },
    async session({ session, token, trigger }) {
      console.log("NextAuth session callback triggered", { trigger, hasToken: !!token, hasSession: !!session })

      if (token) {
        session.user.id = token.id as string
        session.user.name = token.name as string
        session.user.email = token.email as string
        session.user.role = token.role as string
        session.user.subscriptionPlan = token.subscriptionPlan as string

        console.log("NextAuth session callback: Creating custom session cookie", {
          userId: token.id,
          email: token.email,
          role: token.role,
          subscriptionPlan: token.subscriptionPlan
        })
      }
      return session
    },
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id
        token.email = user.email
        token.name = user.name
        token.role = user.role
        token.subscriptionPlan = user.subscriptionPlan
      }
      return token
    },
  },
}

// User registration is handled through the /api/register route
