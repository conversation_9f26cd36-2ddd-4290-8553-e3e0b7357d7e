import { authOptions } from "@/lib/auth"
import { getUserById } from "@/lib/db"
import { getServerSession } from "@/lib/session"
import { getServerSession as getNextAuthSession } from "next-auth"
import { NextResponse } from "next/server"

/**
 * Get current session information
 * Used by client-side components to access session context
 * Checks both NextAuth and custom sessions
 * CRITICAL: Always fetches fresh emailVerified status from database
 */
export async function GET() {
  try {
    // First try NextAuth session
    const nextAuthSession = await getNextAuthSession(authOptions)

    if (nextAuthSession?.user) {
      // CRITICAL FIX: Always fetch fresh emailVerified status from database
      // This ensures verification status changes are immediately reflected
      const freshUser = await getUserById(nextAuthSession.user.id)
      const emailVerified = freshUser?.emailVerified || false

      console.log("Session API: Using NextAuth session", {
        userId: nextAuthSession.user.id,
        email: nextAuthSession.user.email,
        emailVerified: emailVerified,
        role: nextAuthSession.user.role,
        subscriptionPlan: nextAuthSession.user.subscriptionPlan
      })

      return NextResponse.json({
        session: {
          userId: nextAuthSession.user.id,
          email: nextAuthSession.user.email,
          name: nextAuthSession.user.name,
          role: nextAuthSession.user.role || "individual_user",
          subscriptionPlan: nextAuthSession.user.subscriptionPlan || "individual_free",
          permissions: [], // Will be populated based on role
          emailVerified: emailVerified, // Fresh from database
          tenantId: undefined,
          tenantRole: undefined
        },
        source: "nextauth"
      })
    }

    // Fallback to custom session
    const customSession = await getServerSession()

    if (!customSession) {
      console.log("Session API: No session found (neither NextAuth nor custom)")
      return NextResponse.json({ session: null }, { status: 401 })
    }

    // CRITICAL FIX: Also fetch fresh emailVerified status for custom sessions
    const freshUser = await getUserById(customSession.userId)
    const emailVerified = freshUser?.emailVerified || false

    console.log("Session API: Using custom session", {
      userId: customSession.userId,
      email: customSession.email,
      emailVerified: emailVerified,
      role: customSession.role,
      subscriptionPlan: customSession.subscriptionPlan
    })

    return NextResponse.json({
      session: {
        userId: customSession.userId,
        email: customSession.email,
        name: customSession.name,
        role: customSession.role,
        subscriptionPlan: customSession.subscriptionPlan,
        permissions: customSession.permissions,
        emailVerified: emailVerified, // Fresh from database
        // tenantId: customSession.tenantId,
        // tenantRole: customSession.tenantRole
      },
      source: "custom"
    })
  } catch (error) {
    console.error("Session API error:", error)
    return NextResponse.json({ error: "Failed to get session" }, { status: 500 })
  }
}
