/**
 * Shared authentication utilities for login and signup pages
 */

import { logger } from "@/lib/logger";
import { signIn } from "next-auth/react";

// OAuth provider configuration
export const getOAuthProviders = () => {
  const isGoogleEnabled = process.env.NEXT_PUBLIC_GOOGLE_OAUTH_ENABLED === "true";
  const isFacebookEnabled = process.env.NEXT_PUBLIC_FACEBOOK_OAUTH_ENABLED === "true";
  return {
    isGoogleEnabled,
    isFacebookEnabled,
    hasOAuthProviders: isGoogleEnabled || isFacebookEnabled,
  };
};

// OAuth error message mapping
export const getOAuthErrorMessage = (error: string, provider: string): string => {
  switch (error) {
    case "OAuthSignin":
      return `Failed to start ${provider} authentication. Please try again.`;
    case "OAuthCallback":
      return `${provider} authentication was cancelled or failed. Please try again.`;
    case "OAuthCreateAccount":
      return `Failed to create account with ${provider}. Please try again or use email signup.`;
    case "EmailCreateAccount":
      return "An account with this email already exists. Please sign in with your password.";
    case "Callback":
      return "Authentication failed. Please try again.";
    case "OAuthAccountNotLinked":
      return "This account is already linked to another user. Please sign in with your original method.";
    case "EmailSignin":
      return "Failed to send verification email. Please try again.";
    case "CredentialsSignin":
      return "Invalid credentials. Please check your email and password.";
    case "SessionRequired":
      return "Please sign in to access this page.";
    default:
      return `${provider} authentication failed. Please try again.`;
  }
};

// Shared OAuth login handler
export const handleOAuthLogin = async (
  provider: string,
  setIsSSOLoading: (provider: string | null) => void,
  setError: (error: string) => void,
  callbackUrl: string = "/dashboard"
) => {
  setIsSSOLoading(provider);
  setError("");

  const endTimer = logger.authStart(`${provider}-oauth`, "oauth-user");
  logger.info("auth", `${provider} OAuth attempt started`);

  try {
    const result = await signIn(provider, {
      callbackUrl,
      redirect: false,
    });

    if (result?.error) {
      const errorMessage = getOAuthErrorMessage(result.error, provider);
      logger.authFailure(`${provider}-oauth`, result.error, "oauth-user");
      setError(errorMessage);
    } else if (result?.url) {
      logger.authSuccess(`${provider}-oauth`, "oauth-user");
      logger.info("auth", `${provider} OAuth successful, redirecting`, {
        callbackUrl: result.url,
      });
      window.location.href = result.url;
    }
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : "Unknown error";
    logger.error("auth", `${provider} OAuth request failed`, {
      error: errorMessage,
    });

    if (error instanceof Error) {
      if (error.message.includes("network")) {
        setError("Network error. Please check your connection and try again.");
      } else if (error.message.includes("timeout")) {
        setError("Request timed out. Please try again.");
      } else {
        setError(`${provider} authentication failed. Please try again.`);
      }
    } else {
      setError(`${provider} authentication failed. Please try again.`);
    }
  } finally {
    setIsSSOLoading(null);
    endTimer();
  }
};

// Shared form validation utilities
export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

export const validatePassword = (password: string): { isValid: boolean; message?: string } => {
  if (password.length < 8) {
    return { isValid: false, message: "Password must be at least 8 characters long" };
  }
  return { isValid: true };
};

export const validatePasswordMatch = (password: string, confirmPassword: string): boolean => {
  return password === confirmPassword;
};

// Enhanced error handling for API requests with specific authentication error mapping
export const handleApiError = (error: unknown, defaultMessage: string = "Something went wrong. Please try again."): string => {
  if (error instanceof Error) {
    // Handle fetch errors
    if (error.message.includes('fetch')) {
      return "Network error. Please check your connection and try again.";
    }
    return error.message;
  }

  // Handle response errors
  if (typeof error === 'object' && error !== null && 'message' in error) {
    return (error as any).message;
  }

  return defaultMessage;
};

// Enhanced client-side error handler for authentication responses
export const handleAuthResponse = async (response: Response): Promise<{ success: boolean; data?: any; error?: string }> => {
  try {
    const data = await response.json();

    if (response.ok) {
      return { success: true, data };
    }

    // Map specific error codes to user-friendly messages
    let errorMessage = data.error || "Authentication failed";

    switch (response.status) {
      case 400:
        if (data.code === 'VALIDATION_ERROR') {
          errorMessage = "Please check your input and try again.";
        }
        break;
      case 401:
        if (data.error?.includes('Invalid credentials')) {
          errorMessage = "Invalid email or password. Please try again.";
        } else if (data.error?.includes('social login')) {
          errorMessage = "This account was created with social login. Please sign in with Google or add a password in your profile.";
        } else {
          errorMessage = "Invalid credentials. Please try again.";
        }
        break;
      case 403:
        if (data.requiresVerification) {
          errorMessage = "Please verify your email address before signing in.";
        } else {
          errorMessage = "Access denied. Please contact support if this continues.";
        }
        break;
      case 409:
        errorMessage = "An account with this email already exists. Please sign in instead.";
        break;
      case 429:
        errorMessage = "Too many attempts. Please wait a moment before trying again.";
        break;
      case 500:
        errorMessage = "Server error. Please try again in a moment.";
        break;
      default:
        errorMessage = data.error || "Something went wrong. Please try again.";
    }

    return { success: false, error: errorMessage };
  } catch {
    return {
      success: false,
      error: response.status >= 500
        ? "Server error. Please try again in a moment."
        : "Something went wrong. Please try again."
    };
  }
};

// Shared success message formatting
export const formatVerificationMessage = (email: string, isSignup: boolean = false): string => {
  const action = isSignup ? "created successfully! We've sent" : "Please verify your email address before signing in. We've sent";
  return `${isSignup ? "Account " : ""}${action} a verification email to ${email}. Check your inbox and click the verification link to activate your account.`;
};

// Shared redirect delay utility
export const delayedRedirect = (router: any, path: string, delay: number = 2000) => {
  setTimeout(() => {
    logger.navigationStart(window.location.pathname, path, "user");
    router.push(path);
  }, delay);
};

// Force redirect with session refresh (for post-verification redirects)
export const forceRedirectWithSessionRefresh = (path: string, delay: number = 1000) => {
  setTimeout(() => {
    logger.navigationStart(window.location.pathname, path, "user");
    window.location.href = path;
  }, delay);
};
