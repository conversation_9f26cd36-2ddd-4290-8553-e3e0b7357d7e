import { <PERSON><PERSON>eal<PERSON><PERSON><PERSON><PERSON> } from "@/lib/db-debug"
import { logger } from "@/lib/logger"
import { NextResponse } from "next/server"

export async function GET() {
  const startTime = Date.now()

  try {
    logger.info("api", "Health check requested")

    const health = {
      status: "healthy",
      timestamp: new Date().toISOString(),
      version: process.env.npm_package_version || "unknown",
      environment: process.env.NODE_ENV,
      uptime: process.uptime(),
      checks: {
        database: { healthy: false, latency: 0, error: null as string | null },
        uploadthing: { healthy: false, error: null as string | null },
        environment: { healthy: false, missing: [] as string[] },
        performance: { healthy: false, issues: [] as string[] },
      },
      performance: {
        responseTime: 0,
        memoryUsage: process.memoryUsage(),
      },
    }

    // Database health check
    try {
      const dbHealth = await DatabaseHealthChecker.checkHealth()
      health.checks.database = {
        healthy: dbHealth.healthy,
        latency: dbHealth.latency || 0,
        error: dbHealth.error || null,
      }
    } catch (error) {
      health.checks.database = {
        healthy: false,
        latency: 0,
        error: error instanceof Error ? error.message : "Unknown database error",
      }
    }

    // Uploadthing configuration check
    try {
      const hasUploadthingSecret = !!process.env.UPLOADTHING_SECRET
      const hasUploadthingAppId = !!process.env.UPLOADTHING_APP_ID

      health.checks.uploadthing = {
        healthy: hasUploadthingSecret && hasUploadthingAppId,
        error: !hasUploadthingSecret || !hasUploadthingAppId
          ? "Missing Uploadthing configuration"
          : null,
      }
    } catch (error) {
      health.checks.uploadthing = {
        healthy: false,
        error: error instanceof Error ? error.message : "Unknown upload error",
      }
    }

    // Environment variables check (using new validation system)
    try {
      // Import dynamically to avoid circular dependencies
      const { getEnvironmentHealth } = await import("@/lib/environment-validation");
      const envHealth = getEnvironmentHealth();

      health.checks.environment = {
        healthy: envHealth.healthy,
        missing: envHealth.issues,
      }
    } catch {
      health.checks.environment = {
        healthy: false,
        missing: ["Error checking environment variables"],
      }
    }

    // Performance health check
    try {
      // Import dynamically to avoid circular dependencies
      const { getPerformanceHealth } = await import("@/lib/performance-monitoring");
      const perfHealth = getPerformanceHealth();

      health.checks.performance = {
        healthy: perfHealth.healthy,
        issues: perfHealth.issues,
      }
    } catch {
      health.checks.performance = {
        healthy: false,
        issues: ["Error checking performance metrics"],
      }
    }

    // Calculate overall health
    const allChecksHealthy = Object.values(health.checks).every(check => check.healthy)
    health.status = allChecksHealthy ? "healthy" : "unhealthy"

    // Performance metrics
    health.performance.responseTime = Date.now() - startTime

    logger.info("api", "Health check completed", {
      status: health.status,
      responseTime: health.performance.responseTime,
      checks: Object.fromEntries(
        Object.entries(health.checks).map(([key, value]) => [key, value.healthy])
      ),
    })

    return NextResponse.json(health, {
      status: allChecksHealthy ? 200 : 503,
      headers: {
        "Cache-Control": "no-cache, no-store, must-revalidate",
        "Pragma": "no-cache",
        "Expires": "0",
      },
    })
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : "Unknown error"

    logger.error("api", "Health check failed", {
      error: errorMessage,
      responseTime: Date.now() - startTime,
    })

    return NextResponse.json(
      {
        status: "error",
        timestamp: new Date().toISOString(),
        error: errorMessage,
        performance: {
          responseTime: Date.now() - startTime,
        },
      },
      { status: 500 }
    )
  }
}
