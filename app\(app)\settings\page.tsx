"use client";

import {
  <PERSON>,
  Building2,
  Globe,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  Sun,
} from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

import { Alert, AlertDescription } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useTheme } from "next-themes";

import { AccountDeletionSection } from "@/components/account-deletion-section";

export default function SettingsPage() {
  const router = useRouter();
  const { theme, setTheme } = useTheme();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [userSession, setUserSession] = useState<{
    email: string;
    name: string;
  } | null>(null);

  // Mock settings data
  const [settings, setSettings] = useState({
    notifications: {
      email: true,
      push: true,
      expiryReminders: true,
      expiryReminderDays: 30,
      updates: false,
    },
    appearance: {
      theme: "system",
      language: "en",
    },
    privacy: {
      shareData: false,
      analytics: true,
    },
  });

  useEffect(() => {
    const checkAuth = async () => {
      try {
        // Check authentication and get user profile
        const response = await fetch("/api/user/profile", {
          credentials: "include",
        });

        if (!response.ok) {
          if (response.status === 401) {
            router.push("/login");
            return;
          }
        }

        const data = await response.json();
        if (data.success && data.user) {
          setUserSession({
            email: data.user.email,
            name: data.user.name,
          });
        }

        // Simulate loading settings
        setTimeout(() => {
          setIsLoading(false);
        }, 500);
      } catch (error) {
        console.error("Auth check failed:", error);
        router.push("/login");
      }
    };

    checkAuth();
  }, [router]);

  const handleSwitchChange = (
    section: string,
    setting: string,
    checked: boolean
  ) => {
    setSettings((prev) => ({
      ...prev,
      [section]: {
        ...prev[section as keyof typeof prev],
        [setting]: checked,
      },
    }));
  };

  const handleSelectChange = (
    section: string,
    setting: string,
    value: string
  ) => {
    setSettings((prev) => ({
      ...prev,
      [section]: {
        ...prev[section as keyof typeof prev],
        [setting]: value,
      },
    }));

    // Apply theme change immediately
    if (section === "appearance" && setting === "theme") {
      setTheme(value);
    }
  };

  const handleSave = () => {
    setIsSaving(true);

    // Simulate saving settings
    setTimeout(() => {
      setIsSaving(false);
    }, 1000);
  };

  if (isLoading) {
    return (
      <div className="p-6 text-center">
        <div className="animate-pulse">Loading settings...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
        <div className="mt-4 text-center">
          <Button onClick={() => window.location.reload()}>Retry</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 p-6 md:p-8">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Settings</h1>
        <p className="text-muted-foreground">
          Manage your application preferences
        </p>
      </div>

      <Tabs defaultValue="notifications">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="notifications">Notifications</TabsTrigger>
          <TabsTrigger value="appearance">Appearance</TabsTrigger>
          <TabsTrigger value="privacy">Privacy</TabsTrigger>
          <TabsTrigger value="business">Business</TabsTrigger>
          <TabsTrigger value="account">Account</TabsTrigger>
        </TabsList>
        <TabsContent value="notifications" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Bell className="h-5 w-5" /> Notification Settings
              </CardTitle>
              <CardDescription>
                Configure how and when you receive notifications
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="email-notifications" className="font-medium">
                    Email Notifications
                  </Label>
                  <p className="text-sm text-muted-foreground">
                    Receive notifications via email
                  </p>
                </div>
                <Switch
                  id="email-notifications"
                  checked={settings.notifications.email}
                  onCheckedChange={(checked) =>
                    handleSwitchChange("notifications", "email", checked)
                  }
                />
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="push-notifications" className="font-medium">
                    Push Notifications
                  </Label>
                  <p className="text-sm text-muted-foreground">
                    Receive notifications in your browser
                  </p>
                </div>
                <Switch
                  id="push-notifications"
                  checked={settings.notifications.push}
                  onCheckedChange={(checked) =>
                    handleSwitchChange("notifications", "push", checked)
                  }
                />
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="expiry-reminders" className="font-medium">
                    Certificate Expiry Reminders
                  </Label>
                  <p className="text-sm text-muted-foreground">
                    Get notified when certificates are about to expire
                  </p>
                </div>
                <Switch
                  id="expiry-reminders"
                  checked={settings.notifications.expiryReminders}
                  onCheckedChange={(checked) =>
                    handleSwitchChange(
                      "notifications",
                      "expiryReminders",
                      checked
                    )
                  }
                />
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="reminder-days" className="font-medium">
                    Reminder Days Before Expiry
                  </Label>
                  <p className="text-sm text-muted-foreground">
                    How many days before expiry to send reminders
                  </p>
                </div>
                <Select
                  value={settings.notifications.expiryReminderDays.toString()}
                  onValueChange={(value) =>
                    handleSelectChange(
                      "notifications",
                      "expiryReminderDays",
                      value
                    )
                  }
                  disabled={!settings.notifications.expiryReminders}
                >
                  <SelectTrigger className="w-32">
                    <SelectValue placeholder="Select days" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="7">7 days</SelectItem>
                    <SelectItem value="14">14 days</SelectItem>
                    <SelectItem value="30">30 days</SelectItem>
                    <SelectItem value="60">60 days</SelectItem>
                    <SelectItem value="90">90 days</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <Label
                    htmlFor="updates-notifications"
                    className="font-medium"
                  >
                    Product Updates
                  </Label>
                  <p className="text-sm text-muted-foreground">
                    Receive notifications about new features and updates
                  </p>
                </div>
                <Switch
                  id="updates-notifications"
                  checked={settings.notifications.updates}
                  onCheckedChange={(checked) =>
                    handleSwitchChange("notifications", "updates", checked)
                  }
                />
              </div>
            </CardContent>
            <CardFooter>
              <Button onClick={handleSave} disabled={isSaving}>
                {isSaving ? "Saving..." : "Save Changes"}
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
        <TabsContent value="appearance" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Palette className="h-5 w-5" /> Appearance Settings
              </CardTitle>
              <CardDescription>
                Customize how Sailor Plus looks and feels
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="theme" className="font-medium">
                    Theme
                  </Label>
                  <p className="text-sm text-muted-foreground">
                    Choose your preferred theme
                  </p>
                </div>
                <Select
                  value={settings.appearance.theme}
                  onValueChange={(value) =>
                    handleSelectChange("appearance", "theme", value)
                  }
                >
                  <SelectTrigger className="w-32">
                    <SelectValue placeholder="Select theme" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem
                      value="light"
                      className="flex items-center gap-2"
                    >
                      <Sun className="h-4 w-4" /> Light
                    </SelectItem>
                    <SelectItem
                      value="dark"
                      className="flex items-center gap-2"
                    >
                      <Moon className="h-4 w-4" /> Dark
                    </SelectItem>
                    <SelectItem
                      value="system"
                      className="flex items-center gap-2"
                    >
                      <div className="flex">
                        <Sun className="h-4 w-4" />
                        <Moon className="h-4 w-4 ml-1" />
                      </div>
                      System
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="language" className="font-medium">
                    Language
                  </Label>
                  <p className="text-sm text-muted-foreground">
                    Select your preferred language
                  </p>
                </div>
                <Select
                  value={settings.appearance.language}
                  onValueChange={(value) =>
                    handleSelectChange("appearance", "language", value)
                  }
                >
                  <SelectTrigger className="w-32">
                    <SelectValue placeholder="Select language" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="en">
                      <div className="flex items-center gap-2">
                        <Globe className="h-4 w-4" /> English
                      </div>
                    </SelectItem>
                    <SelectItem value="es">
                      <div className="flex items-center gap-2">
                        <Globe className="h-4 w-4" /> Español
                      </div>
                    </SelectItem>
                    <SelectItem value="fr">
                      <div className="flex items-center gap-2">
                        <Globe className="h-4 w-4" /> Français
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
            <CardFooter>
              <Button onClick={handleSave} disabled={isSaving}>
                {isSaving ? "Saving..." : "Save Changes"}
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
        <TabsContent value="privacy" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" /> Privacy Settings
              </CardTitle>
              <CardDescription>
                Manage your data and privacy preferences
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="share-data" className="font-medium">
                    Share Certificate Data
                  </Label>
                  <p className="text-sm text-muted-foreground">
                    Allow sharing of anonymized certificate data for industry
                    insights
                  </p>
                </div>
                <Switch
                  id="share-data"
                  checked={settings.privacy.shareData}
                  onCheckedChange={(checked) =>
                    handleSwitchChange("privacy", "shareData", checked)
                  }
                />
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="analytics" className="font-medium">
                    Usage Analytics
                  </Label>
                  <p className="text-sm text-muted-foreground">
                    Allow collection of anonymous usage data to improve the
                    application
                  </p>
                </div>
                <Switch
                  id="analytics"
                  checked={settings.privacy.analytics}
                  onCheckedChange={(checked) =>
                    handleSwitchChange("privacy", "analytics", checked)
                  }
                />
              </div>
              <div className="pt-4">
                <Button variant="outline" className="w-full">
                  Download My Data
                </Button>
              </div>
            </CardContent>
            <CardFooter>
              <Button onClick={handleSave} disabled={isSaving}>
                {isSaving ? "Saving..." : "Save Changes"}
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
        <TabsContent value="business" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building2 className="h-5 w-5" /> Organizations
              </CardTitle>
              <CardDescription>
                Create and manage your yacht or training provider organization
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="text-center py-8">
                <Building2 className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                <h3 className="text-lg font-semibold mb-2">
                  Ready to create your organization?
                </h3>
                <p className="text-muted-foreground mb-6 max-w-md mx-auto">
                  Create a yacht profile for crew management or set up a
                  training provider for course delivery.
                </p>
                <Button asChild size="lg">
                  <Link href="/organizations/new">Create Organization</Link>
                </Button>
                <div className="mt-6 p-4 bg-muted rounded-lg">
                  <p className="text-sm text-muted-foreground">
                    <strong>Choose your organization type:</strong> Create a
                    yacht profile for crew management and job posting, or set up
                    a training provider for course delivery and certification.
                    You can create multiple organizations and switch between
                    them anytime.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="account" className="mt-4">
          {userSession ? (
            <AccountDeletionSection
              userEmail={userSession.email}
              userName={userSession.name}
            />
          ) : (
            <Card>
              <CardContent className="p-6">
                <div className="text-center text-muted-foreground">
                  Loading account information...
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
