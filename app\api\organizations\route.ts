import { addUserToOrganization, createOrganization } from "@/lib/db";
import { getServerSession } from "@/lib/session";
import { nanoid } from "nanoid";
import { NextResponse } from "next/server";

export async function POST(req: Request) {
  try {
    // Check authentication
    const session = await getServerSession();
    if (!session) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    const { name, type, description, website } = await req.json();

    // Validate required fields
    if (!name || !type) {
      return NextResponse.json(
        { error: "Organization name and type are required" },
        { status: 400 }
      );
    }

    // Validate organization type
    if (!["yacht_company", "cert_provider"].includes(type)) {
      return NextResponse.json(
        { error: "Invalid organization type" },
        { status: 400 }
      );
    }

    // Generate organization ID
    const organizationId = nanoid();

    // Create organization (pending verification for premium features)
    await createOrganization({
      id: organizationId,
      name: name.trim(),
      type,
      contactEmail: session.email, // Use user's email as organization contact
      description: description?.trim() || undefined,
      website: website?.trim() || undefined,
    });

    // Add user as organization owner
    await addUserToOrganization(
      session.userId,
      organizationId,
      "owner"
    );

    return NextResponse.json(
      {
        success: true,
        organization: {
          id: organizationId,
          name: name.trim(),
          type,
          status: "pending",
        },
        message: "Organization created successfully! You have immediate access to basic features. Verification is required for premium features like job posting and public listings.",
      },
      { status: 201 }
    );
  } catch (error) {
    console.error("Organization creation error:", error);
    return NextResponse.json(
      { error: "Something went wrong. Please try again." },
      { status: 500 }
    );
  }
}

export async function GET() {
  try {
    // Check authentication
    const session = await getServerSession();
    if (!session) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    // For now, return empty array - will implement user organizations endpoint later
    return NextResponse.json({
      success: true,
      organizations: [],
    });
  } catch (error) {
    console.error("Get organizations error:", error);
    return NextResponse.json(
      { error: "Something went wrong. Please try again." },
      { status: 500 }
    );
  }
}
