import { Badge } from "@/components/ui/badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { UserRole } from "@/lib/auth-config";
import {
  getAdminCertificateStats,
  getAdminOrganizationStats,
  getAdminUserStats,
} from "@/lib/db";
import { getServerSession } from "@/lib/session";
import Link from "next/link";
import { redirect } from "next/navigation";
import { Suspense } from "react";

// Force dynamic rendering for admin pages that use session/cookies
export const dynamic = "force-dynamic";

async function getAdminStats() {
  try {
    // Get real data from database
    const [userStats, organizationStats, certificateStats] = await Promise.all([
      getAdminUserStats(),
      getAdminOrganizationStats(),
      getAdminCertificateStats(),
    ]);

    return {
      users: userStats,
      organizations: organizationStats,
      certificates: certificateStats,
    };
  } catch (error) {
    console.error("Error fetching admin stats:", error);
    return {
      users: { total: 0, individual: 0, systemAdmins: 0, recent: 0 },
      organizations: { total: 0, pending: 0, verified: 0, recent: 0 },
      certificates: { total: 0, recent: 0 },
    };
  }
}

function StatCard({
  title,
  value,
  description,
  badge,
}: {
  title: string;
  value: number | undefined;
  description: string;
  badge?: {
    text: string;
    variant: "default" | "secondary" | "destructive" | "outline";
  };
}) {
  // Handle undefined values gracefully
  const displayValue = value ?? 0;

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        {badge && <Badge variant={badge.variant}>{badge.text}</Badge>}
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">
          {displayValue.toLocaleString()}
        </div>
        <p className="text-xs text-muted-foreground">{description}</p>
      </CardContent>
    </Card>
  );
}

async function AdminStatsContent() {
  const stats = await getAdminStats();

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <StatCard
        title="Total Users"
        value={stats.users?.total}
        description={`${stats.users?.recent ?? 0} new this week`}
      />

      <StatCard
        title="Individual Users"
        value={stats.users?.individual}
        description="Maritime professionals"
      />

      <StatCard
        title="Organizations"
        value={stats.organizations?.total}
        description={`${stats.organizations?.recent ?? 0} new this week`}
        badge={
          (stats.organizations?.pending ?? 0) > 0
            ? {
                text: `${stats.organizations?.pending ?? 0} pending`,
                variant: "destructive",
              }
            : undefined
        }
      />

      <StatCard
        title="Certificates"
        value={stats.certificates?.total}
        description={`${stats.certificates?.recent ?? 0} added this week`}
      />

      <StatCard
        title="Verified Organizations"
        value={stats.organizations?.verified}
        description="Active business accounts"
      />

      <StatCard
        title="System Admins"
        value={stats.users?.systemAdmins}
        description="Administrative users"
      />
    </div>
  );
}

export default async function AdminDashboard() {
  const session = await getServerSession();

  // Double-check authentication (layout should handle this, but be safe)
  if (!session || session.role !== UserRole.SYSTEM_ADMIN) {
    redirect("/dashboard?error=unauthorized");
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Admin Dashboard</h1>
        <p className="text-muted-foreground">
          System overview and management tools for Sailor Plus Maritime Platform
        </p>
      </div>

      <Suspense
        fallback={
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            {Array.from({ length: 6 }).map((_, i) => (
              <Card key={i}>
                <CardHeader className="space-y-0 pb-2">
                  <div className="h-4 bg-gray-200 rounded animate-pulse" />
                </CardHeader>
                <CardContent>
                  <div className="h-8 bg-gray-200 rounded animate-pulse mb-2" />
                  <div className="h-3 bg-gray-200 rounded animate-pulse" />
                </CardContent>
              </Card>
            ))}
          </div>
        }
      >
        <AdminStatsContent />
      </Suspense>

      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>Common administrative tasks</CardDescription>
          </CardHeader>
          <CardContent className="space-y-2">
            <a
              href="/admin/users"
              className="block w-full text-left p-3 rounded-md border border-gray-200 hover:bg-gray-50 transition-colors"
            >
              <div className="font-medium">Manage Users</div>
              <div className="text-sm text-gray-500">
                View and manage user accounts
              </div>
            </a>
            <Link
              href="/admin/organizations"
              className="block w-full text-left p-3 rounded-md border border-gray-200 hover:bg-gray-50 transition-colors"
            >
              <div className="font-medium">Review Organizations</div>
              <div className="text-sm text-gray-500">
                Verify pending organization requests
              </div>
            </Link>
            <a
              href="/admin/system"
              className="block w-full text-left p-3 rounded-md border border-gray-200 hover:bg-gray-50 transition-colors"
            >
              <div className="font-medium">System Health</div>
              <div className="text-sm text-gray-500">
                Monitor system performance and health
              </div>
            </a>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>System Status</CardTitle>
            <CardDescription>Current system health indicators</CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm">Database</span>
              <Badge variant="default">Healthy</Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">File Storage</span>
              <Badge variant="default">Operational</Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">Email Service</span>
              <Badge variant="default">Active</Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">Authentication</span>
              <Badge variant="default">Secure</Badge>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
