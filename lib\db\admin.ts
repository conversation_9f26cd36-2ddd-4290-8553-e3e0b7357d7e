/**
 * Admin and System Functions
 *
 * This module handles admin-specific database operations including:
 * - System health monitoring
 * - Account deletion audits
 * - Admin dashboard queries
 * - System statistics and monitoring
 */

import { neon } from "@neondatabase/serverless"
import { desc, eq, gte } from "drizzle-orm"
import { drizzle } from "drizzle-orm/neon-http"
import {
  accountDeletionAudits,
  certificates,
  cleanupJobs,
  organizations,
  users
} from "./schema"

// Initialize database connection for this module
const sql = neon(process.env.DATABASE_URL!)
const db = drizzle(sql)

// ============================================================================
// SYSTEM HEALTH AND MONITORING
// ============================================================================

/**
 * Get system health data for admin dashboard
 */
export async function getSystemHealthData() {
  try {
    const startTime = Date.now()

    // Test database connectivity
    await db.select().from(users).limit(1)
    const dbLatency = Date.now() - startTime

    // Get recent cleanup jobs
    const recentJobs = await db
      .select()
      .from(cleanupJobs)
      .orderBy(desc(cleanupJobs.createdAt))
      .limit(10)

    // Get recent account deletion audits
    const recentAudits = await db
      .select()
      .from(accountDeletionAudits)
      .orderBy(desc(accountDeletionAudits.createdAt))
      .limit(10)

    return {
      database: {
        healthy: true,
        latency: dbLatency,
      },
      storage: {
        healthy: true, // Assume healthy if no errors
        usage: "Unknown", // Would need external API to get real usage
      },
      email: {
        healthy: true, // Assume healthy if no errors
        lastSent: new Date(), // Would need to track actual email sends
      },
      auth: {
        healthy: true, // Assume healthy if no errors
        activeSessions: 0, // Would need session tracking to get real count
      },
      recentJobs: recentJobs.map(job => ({
        id: job.id,
        jobType: job.jobType,
        status: job.status,
        createdAt: job.createdAt,
        metadata: job.metadata ? JSON.parse(job.metadata) : null,
      })),
      recentAudits: recentAudits.map(audit => ({
        id: audit.id,
        deletionType: audit.deletionType,
        userEmail: audit.userEmail,
        initiatedBy: audit.initiatedBy,
        createdAt: audit.createdAt,
      })),
    }
  } catch (error) {
    console.error("Error fetching system health data:", error)
    return {
      database: { healthy: false, latency: 0 },
      storage: { healthy: false, usage: "Unknown" },
      email: { healthy: false, lastSent: null },
      auth: { healthy: false, activeSessions: 0 },
      recentJobs: [],
      recentAudits: [],
    }
  }
}

/**
 * Get system statistics for admin dashboard
 */
export async function getSystemStats() {
  try {
    // Get user stats
    const totalUsers = await db.select().from(users)
    const activeUsers = totalUsers.filter(user => !user.deletedAt)
    const deletedUsers = totalUsers.filter(user => user.deletedAt)

    // Get organization stats
    const totalOrgs = await db.select().from(organizations)
    const pendingOrgs = totalOrgs.filter(org => org.status === "pending")
    const verifiedOrgs = totalOrgs.filter(org => org.status === "verified")

    // Get certificate stats
    const totalCerts = await db.select().from(certificates)
    const today = new Date()
    const expiredCerts = totalCerts.filter(cert =>
      cert.expiryDate && cert.expiryDate < today
    )

    // Get recent activity (last 30 days)
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(today.getDate() - 30)

    const recentUsers = totalUsers.filter(user =>
      user.createdAt >= thirtyDaysAgo && !user.deletedAt
    )
    const recentOrgs = totalOrgs.filter(org =>
      org.createdAt >= thirtyDaysAgo
    )
    const recentCerts = totalCerts.filter(cert =>
      cert.createdAt >= thirtyDaysAgo
    )

    return {
      users: {
        total: totalUsers.length,
        active: activeUsers.length,
        deleted: deletedUsers.length,
        recent: recentUsers.length,
      },
      organizations: {
        total: totalOrgs.length,
        pending: pendingOrgs.length,
        verified: verifiedOrgs.length,
        recent: recentOrgs.length,
      },
      certificates: {
        total: totalCerts.length,
        expired: expiredCerts.length,
        recent: recentCerts.length,
      },
    }
  } catch (error) {
    console.error("Error getting system stats:", error)
    return {
      users: { total: 0, active: 0, deleted: 0, recent: 0 },
      organizations: { total: 0, pending: 0, verified: 0, recent: 0 },
      certificates: { total: 0, expired: 0, recent: 0 },
    }
  }
}

/**
 * Get admin user statistics
 */
export async function getAdminUserStats() {
  try {
    const totalUsers = await db.select().from(users)
    const individualUsers = totalUsers.filter(user =>
      user.role === "individual_user" && !user.deletedAt
    )
    const systemAdmins = totalUsers.filter(user =>
      user.role === "system_admin" && !user.deletedAt
    )

    const today = new Date()
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(today.getDate() - 30)

    const recentUsers = totalUsers.filter(user =>
      user.createdAt >= thirtyDaysAgo && !user.deletedAt
    )

    return {
      total: totalUsers.length,
      individual: individualUsers.length,
      systemAdmins: systemAdmins.length,
      recent: recentUsers.length,
    }
  } catch (error) {
    console.error("Error getting admin user stats:", error)
    return { total: 0, individual: 0, systemAdmins: 0, recent: 0 }
  }
}

/**
 * Get admin organization statistics
 */
export async function getAdminOrganizationStats() {
  try {
    const totalOrgs = await db.select().from(organizations)
    const pendingOrgs = totalOrgs.filter(org => org.status === "pending")
    const verifiedOrgs = totalOrgs.filter(org => org.status === "verified")

    const today = new Date()
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(today.getDate() - 30)

    const recentOrgs = totalOrgs.filter(org =>
      org.createdAt >= thirtyDaysAgo
    )

    return {
      total: totalOrgs.length,
      pending: pendingOrgs.length,
      verified: verifiedOrgs.length,
      recent: recentOrgs.length,
    }
  } catch (error) {
    console.error("Error getting admin organization stats:", error)
    return { total: 0, pending: 0, verified: 0, recent: 0 }
  }
}

/**
 * Get admin certificate statistics
 */
export async function getAdminCertificateStats() {
  try {
    const totalCerts = await db.select().from(certificates)
    const today = new Date()
    const expiredCerts = totalCerts.filter(cert =>
      cert.expiryDate && cert.expiryDate < today
    )

    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(today.getDate() - 30)

    const recentCerts = totalCerts.filter(cert =>
      cert.createdAt >= thirtyDaysAgo
    )

    return {
      total: totalCerts.length,
      expired: expiredCerts.length,
      recent: recentCerts.length,
    }
  } catch (error) {
    console.error("Error getting admin certificate stats:", error)
    return { total: 0, expired: 0, recent: 0 }
  }
}

// ============================================================================
// ACCOUNT DELETION AUDIT FUNCTIONS
// ============================================================================

/**
 * Get account deletion audits with pagination
 */
export async function getAccountDeletionAudits(limit = 50, offset = 0) {
  try {
    const audits = await db
      .select()
      .from(accountDeletionAudits)
      .orderBy(desc(accountDeletionAudits.createdAt))
      .limit(limit)
      .offset(offset)

    return audits.map(audit => ({
      ...audit,
      dataRetained: audit.dataRetained ? JSON.parse(audit.dataRetained) : null,
      dataDeleted: audit.dataDeleted ? JSON.parse(audit.dataDeleted) : null,
    }))
  } catch (error) {
    console.error("Error getting account deletion audits:", error)
    return []
  }
}

/**
 * Get account deletion audit by user ID
 */
export async function getAccountDeletionAuditByUserId(userId: string) {
  try {
    const audits = await db
      .select()
      .from(accountDeletionAudits)
      .where(eq(accountDeletionAudits.userId, userId))
      .orderBy(desc(accountDeletionAudits.createdAt))

    return audits.map(audit => ({
      ...audit,
      dataRetained: audit.dataRetained ? JSON.parse(audit.dataRetained) : null,
      dataDeleted: audit.dataDeleted ? JSON.parse(audit.dataDeleted) : null,
    }))
  } catch (error) {
    console.error("Error getting account deletion audit by user ID:", error)
    return []
  }
}

/**
 * Get recent account deletion audits
 */
export async function getRecentAccountDeletionAudits(days = 30) {
  try {
    const cutoffDate = new Date()
    cutoffDate.setDate(cutoffDate.getDate() - days)

    const audits = await db
      .select()
      .from(accountDeletionAudits)
      .where(gte(accountDeletionAudits.createdAt, cutoffDate))
      .orderBy(desc(accountDeletionAudits.createdAt))

    return audits.map(audit => ({
      ...audit,
      dataRetained: audit.dataRetained ? JSON.parse(audit.dataRetained) : null,
      dataDeleted: audit.dataDeleted ? JSON.parse(audit.dataDeleted) : null,
    }))
  } catch (error) {
    console.error("Error getting recent account deletion audits:", error)
    return []
  }
}

// ============================================================================
// ADMIN DASHBOARD QUERIES
// ============================================================================

/**
 * Get admin dashboard overview data
 */
export async function getAdminDashboardData() {
  try {
    const [systemStats, systemHealth, recentAudits] = await Promise.all([
      getSystemStats(),
      getSystemHealthData(),
      getRecentAccountDeletionAudits(7), // Last 7 days
    ])

    return {
      stats: systemStats,
      health: systemHealth,
      recentActivity: {
        audits: recentAudits.slice(0, 5), // Latest 5 audits
        jobs: systemHealth.recentJobs.slice(0, 5), // Latest 5 jobs
      },
    }
  } catch (error) {
    console.error("Error getting admin dashboard data:", error)
    return {
      stats: {
        users: { total: 0, active: 0, deleted: 0, recent: 0 },
        organizations: { total: 0, pending: 0, verified: 0, recent: 0 },
        certificates: { total: 0, expired: 0, recent: 0 },
      },
      health: {
        database: { healthy: false, latency: 0 },
        storage: { healthy: false, usage: "Unknown" },
        email: { healthy: false, lastSent: null },
        auth: { healthy: false, activeSessions: 0 },
        recentJobs: [],
        recentAudits: [],
      },
      recentActivity: {
        audits: [],
        jobs: [],
      },
    }
  }
}

/**
 * Search across all entities for admin
 */
export async function adminSearch(query: string, limit = 20) {
  try {
    const searchTerm = query.toLowerCase()

    // Search users
    const allUsers = await db.select().from(users)
    const matchingUsers = allUsers
      .filter(user =>
        user.email.toLowerCase().includes(searchTerm) ||
        user.name.toLowerCase().includes(searchTerm)
      )
      .slice(0, limit)

    // Search organizations
    const allOrgs = await db.select().from(organizations)
    const matchingOrgs = allOrgs
      .filter(org =>
        org.name.toLowerCase().includes(searchTerm) ||
        org.contactEmail.toLowerCase().includes(searchTerm)
      )
      .slice(0, limit)

    // Search certificates
    const allCerts = await db.select().from(certificates)
    const matchingCerts = allCerts
      .filter(cert =>
        cert.name.toLowerCase().includes(searchTerm) ||
        cert.certificateNumber.toLowerCase().includes(searchTerm) ||
        cert.issuingAuthority.toLowerCase().includes(searchTerm)
      )
      .slice(0, limit)

    return {
      users: matchingUsers,
      organizations: matchingOrgs,
      certificates: matchingCerts,
    }
  } catch (error) {
    console.error("Error performing admin search:", error)
    return {
      users: [],
      organizations: [],
      certificates: [],
    }
  }
}
