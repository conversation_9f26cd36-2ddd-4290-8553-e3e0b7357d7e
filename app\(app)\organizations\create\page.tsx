"use client";

import { <PERSON><PERSON><PERSON><PERSON>, Building2 } from "lucide-react";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import React, { Suspense, useEffect, useState } from "react";

import { Alert, AlertDescription } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { handleApiError } from "@/lib/auth-utils";
import { logger } from "@/lib/logger";

function CreateOrganizationForm() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [successMessage, setSuccessMessage] = useState("");
  const [formData, setFormData] = useState({
    name: "",
    type: "" as "yacht_company" | "cert_provider" | "",
    description: "",
    website: "",
  });

  // Set the type from URL parameter if provided
  useEffect(() => {
    const typeParam = searchParams.get("type");
    if (typeParam === "yacht_company" || typeParam === "cert_provider") {
      setFormData((prev) => ({ ...prev, type: typeParam }));
    }
  }, [searchParams]);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError("");
    setSuccessMessage("");

    const endTimer = logger.authStart("organization-creation", formData.name);
    logger.info("organization", "Organization creation attempt started", {
      name: logger.isLoggingEnabled()
        ? formData.name.substring(0, 3) + "***"
        : undefined,
      type: formData.type,
    });

    // Validate required fields
    if (!formData.name.trim()) {
      setError("Company/Provider name is required");
      setIsLoading(false);
      endTimer();
      return;
    }
    if (!formData.type) {
      setError("Business type is required");
      setIsLoading(false);
      endTimer();
      return;
    }

    try {
      const apiEndTimer = logger.apiRequest("POST", "/api/organizations");

      const requestBody = {
        name: formData.name.trim(),
        type: formData.type,
        description: formData.description.trim() || undefined,
        website: formData.website.trim() || undefined,
      };

      const response = await fetch("/api/organizations", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestBody),
      });

      apiEndTimer();
      logger.apiResponse("POST", "/api/organizations", response.status);

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Something went wrong");
      }

      logger.authSuccess(
        "organization-creation",
        data.organization?.id || "unknown-org"
      );

      setSuccessMessage(
        "Business account created successfully! Your account is pending verification by our admin team."
      );

      // Redirect to dashboard after a short delay
      setTimeout(() => {
        router.push("/dashboard");
      }, 2000);
    } catch (error) {
      const errorMessage = handleApiError(error);
      logger.authFailure("organization-creation", errorMessage, formData.name);
      logger.error("organization", "Organization creation failed", {
        error: errorMessage,
        name: formData.name.substring(0, 3) + "***",
      });
      setError(errorMessage);
      setIsLoading(false);
    } finally {
      setIsLoading(false);
      endTimer();
    }
  };

  return (
    <div className="flex-1 w-full space-y-6 p-6 md:p-8 pt-6">
      <div className="flex items-center gap-4 mb-6">
        <Button variant="ghost" size="sm" asChild>
          <Link href="/dashboard">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Dashboard
          </Link>
        </Button>
      </div>

      <div className="max-w-2xl mx-auto">
        <Card>
          <CardHeader className="space-y-1">
            <div className="flex justify-center mb-2">
              <Building2 className="h-10 w-10 text-primary" />
            </div>
            <CardTitle className="text-2xl text-center">
              Create Business Account
            </CardTitle>
            <CardDescription className="text-center">
              Set up your yacht company or training provider account
            </CardDescription>
          </CardHeader>

          <form onSubmit={handleSubmit}>
            <CardContent className="space-y-4">
              {error && (
                <Alert variant="destructive">
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}
              {successMessage && (
                <Alert
                  variant="default"
                  className="border-green-200 bg-green-50"
                >
                  <AlertDescription className="text-green-800">
                    {successMessage}
                  </AlertDescription>
                </Alert>
              )}

              <div className="space-y-2">
                <Label htmlFor="name">Company/Provider Name *</Label>
                <Input
                  id="name"
                  name="name"
                  placeholder="Enter your company or provider name"
                  required
                  value={formData.name}
                  onChange={handleChange}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="type">Business Type *</Label>
                <Select
                  value={formData.type}
                  onValueChange={(value) => handleSelectChange("type", value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select business type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="yacht_company">Yacht Company</SelectItem>
                    <SelectItem value="cert_provider">
                      Training Provider
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  name="description"
                  placeholder="Brief description of your business (optional)"
                  rows={3}
                  value={formData.description}
                  onChange={handleChange}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="website">Website</Label>
                <Input
                  id="website"
                  name="website"
                  type="url"
                  placeholder="https://www.example.com (optional)"
                  value={formData.website}
                  onChange={handleChange}
                />
              </div>

              <Alert variant="default" className="border-blue-200 bg-blue-50">
                <AlertDescription className="text-blue-800">
                  Your business account will be pending verification by our
                  admin team. You&apos;ll receive an email once it&apos;s
                  approved and can start managing business certificates and
                  accessing advanced features.
                </AlertDescription>
              </Alert>
            </CardContent>

            <CardFooter className="flex flex-col space-y-4">
              <Button
                className="w-full"
                type="submit"
                disabled={isLoading || !!successMessage}
              >
                {isLoading
                  ? "Creating Business Account..."
                  : successMessage
                  ? "Business Account Created!"
                  : "Create Business Account"}
              </Button>

              <div className="text-center text-sm">
                <Link
                  href="/dashboard"
                  className="text-muted-foreground hover:text-primary"
                >
                  Cancel and return to dashboard
                </Link>
              </div>
            </CardFooter>
          </form>
        </Card>
      </div>
    </div>
  );
}

export default function CreateOrganizationPage() {
  return (
    <Suspense
      fallback={
        <div className="flex-1 w-full space-y-6 p-6 md:p-8 pt-6">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <Building2 className="h-8 w-8 animate-pulse text-primary mx-auto mb-2" />
              <p className="text-muted-foreground">Loading...</p>
            </div>
          </div>
        </div>
      }
    >
      <CreateOrganizationForm />
    </Suspense>
  );
}
