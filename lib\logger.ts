// Production-ready logging utility for debugging authentication flow
// Can be enabled/disabled via environment variables

type LogLevel = 'debug' | 'info' | 'warn' | 'error'
type LogContext = 'auth' | 'api' | 'navigation' | 'component' | 'database' | 'upload' | 'admin' | 'cron' | 'email' | 'environment' | 'performance' | 'error-tracking' | 'debug' | 'organization' | 'instrumentation'

interface LogEntry {
  timestamp: string
  level: LogLevel
  context: LogContext
  message: string
  data?: any
  duration?: number
  userId?: string
  sessionId?: string
}

class Logger {
  private isEnabled: boolean
  private logLevel: LogLevel
  private contexts: Set<LogContext>

  constructor() {
    // Enable logging based on environment variables
    this.isEnabled = process.env.NODE_ENV === 'development' ||
      process.env.NEXT_PUBLIC_ENABLE_LOGGING === 'true'

    // Set log level (default to 'info' in production, 'debug' in development)
    this.logLevel = (process.env.NEXT_PUBLIC_LOG_LEVEL as LogLevel) ||
      (process.env.NODE_ENV === 'development' ? 'debug' : 'info')

    // Set which contexts to log (default to all in development)
    const enabledContexts = process.env.NEXT_PUBLIC_LOG_CONTEXTS?.split(',') ||
      ['auth', 'api', 'navigation', 'component', 'database', 'upload']
    this.contexts = new Set(enabledContexts as LogContext[])
  }

  private shouldLog(level: LogLevel, context: LogContext): boolean {
    if (!this.isEnabled) return false
    if (!this.contexts.has(context)) return false

    const levels: LogLevel[] = ['debug', 'info', 'warn', 'error']
    const currentLevelIndex = levels.indexOf(this.logLevel)
    const messageLevelIndex = levels.indexOf(level)

    return messageLevelIndex >= currentLevelIndex
  }

  private formatLog(entry: LogEntry): string {
    const { timestamp, level, context, message, duration, userId, sessionId } = entry

    let logMessage = `[${timestamp}] ${level.toUpperCase()} [${context}] ${message}`

    if (userId) logMessage += ` | User: ${userId}`
    if (sessionId) logMessage += ` | Session: ${sessionId.substring(0, 8)}...`
    if (duration !== undefined) logMessage += ` | Duration: ${duration}ms`

    return logMessage
  }

  private createLogEntry(
    level: LogLevel,
    context: LogContext,
    message: string,
    data?: any,
    duration?: number,
    userId?: string,
    sessionId?: string
  ): LogEntry {
    return {
      timestamp: new Date().toISOString(),
      level,
      context,
      message,
      data,
      duration,
      userId,
      sessionId
    }
  }

  private log(entry: LogEntry): void {
    if (!this.shouldLog(entry.level, entry.context)) return

    const formattedMessage = this.formatLog(entry)

    // Use appropriate console method based on level
    switch (entry.level) {
      case 'debug':
        console.debug(formattedMessage, entry.data || '')
        break
      case 'info':
        console.info(formattedMessage, entry.data || '')
        break
      case 'warn':
        console.warn(formattedMessage, entry.data || '')
        break
      case 'error':
        console.error(formattedMessage, entry.data || '')
        break
    }
  }

  // Public logging methods
  debug(context: LogContext, message: string, data?: any, userId?: string, sessionId?: string): void {
    const entry = this.createLogEntry('debug', context, message, data, undefined, userId, sessionId)
    this.log(entry)
  }

  info(context: LogContext, message: string, data?: any, userId?: string, sessionId?: string): void {
    const entry = this.createLogEntry('info', context, message, data, undefined, userId, sessionId)
    this.log(entry)
  }

  warn(context: LogContext, message: string, data?: any, userId?: string, sessionId?: string): void {
    const entry = this.createLogEntry('warn', context, message, data, undefined, userId, sessionId)
    this.log(entry)
  }

  error(context: LogContext, message: string, data?: any, userId?: string, sessionId?: string): void {
    const entry = this.createLogEntry('error', context, message, data, undefined, userId, sessionId)
    this.log(entry)
  }

  // Timing utilities for performance tracking
  startTimer(context: LogContext, operation: string, userId?: string, sessionId?: string): () => void {
    const startTime = Date.now()
    this.debug(context, `Starting: ${operation}`, undefined, userId, sessionId)

    return () => {
      const duration = Date.now() - startTime
      const entry = this.createLogEntry('info', context, `Completed: ${operation}`, undefined, duration, userId, sessionId)
      this.log(entry)
    }
  }

  // Authentication specific logging
  authStart(operation: string, email?: string): () => void {
    const sessionId = this.generateSessionId()
    this.info('auth', `Auth operation started: ${operation}`, { email: email ? this.maskEmail(email) : undefined }, undefined, sessionId)

    return this.startTimer('auth', operation, undefined, sessionId)
  }

  authSuccess(operation: string, userId: string, sessionId?: string): void {
    this.info('auth', `Auth operation successful: ${operation}`, undefined, userId, sessionId)
  }

  authFailure(operation: string, error: string, email?: string, sessionId?: string): void {
    this.warn('auth', `Auth operation failed: ${operation}`, {
      error,
      email: email ? this.maskEmail(email) : undefined
    }, undefined, sessionId)
  }

  // API request logging
  apiRequest(method: string, url: string, userId?: string, sessionId?: string): () => void {
    this.debug('api', `API Request: ${method} ${url}`, undefined, userId, sessionId)
    return this.startTimer('api', `${method} ${url}`, userId, sessionId)
  }

  apiResponse(method: string, url: string, status: number, userId?: string, sessionId?: string): void {
    const level = status >= 400 ? 'warn' : 'info'
    this.log(this.createLogEntry(level, 'api', `API Response: ${method} ${url}`, { status }, undefined, userId, sessionId))
  }

  // Navigation logging
  navigationStart(from: string, to: string, userId?: string): void {
    this.debug('navigation', `Navigation: ${from} → ${to}`, undefined, userId)
  }

  navigationComplete(to: string, userId?: string): void {
    this.info('navigation', `Navigation completed: ${to}`, undefined, userId)
  }

  // Component lifecycle logging
  componentMount(componentName: string, userId?: string): void {
    this.debug('component', `Component mounted: ${componentName}`, undefined, userId)
  }

  componentUnmount(componentName: string, userId?: string): void {
    this.debug('component', `Component unmounted: ${componentName}`, undefined, userId)
  }

  // Database operation logging
  dbOperation(operation: string, table: string, userId?: string): () => void {
    this.debug('database', `DB Operation: ${operation} on ${table}`, undefined, userId)
    return this.startTimer('database', `${operation} ${table}`, userId)
  }

  // Utility methods
  private maskEmail(email: string): string {
    const [local, domain] = email.split('@')
    if (local.length <= 2) return email
    return `${local.substring(0, 2)}***@${domain}`
  }

  private generateSessionId(): string {
    return Math.random().toString(36).substring(2, 15)
  }

  // Method to check if logging is enabled (useful for conditional expensive operations)
  isLoggingEnabled(context?: LogContext): boolean {
    if (!this.isEnabled) return false
    if (context && !this.contexts.has(context)) return false
    return true
  }
}

// Export singleton instance
export const logger = new Logger()

// Production error tracking integration
export class ProductionErrorTracker {
  private static isEnabled = process.env.NODE_ENV === 'production' &&
    process.env.NEXT_PUBLIC_ERROR_TRACKING_ENABLED === 'true'

  static captureException(error: Error, context?: any) {
    if (!this.isEnabled) return

    // Log locally first
    logger.error('error-tracking', 'Exception captured', {
      message: error.message,
      stack: error.stack,
      context,
      timestamp: new Date().toISOString(),
      userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'server',
      url: typeof window !== 'undefined' ? window.location.href : 'server',
    })

    // In a real implementation, you would send to services like:
    // - Sentry: Sentry.captureException(error, { extra: context })
    // - LogRocket: LogRocket.captureException(error)
    // - Bugsnag: Bugsnag.notify(error, context)
    // - Custom endpoint: fetch('/api/errors', { method: 'POST', body: JSON.stringify({...}) })
  }

  static captureMessage(message: string, level: 'info' | 'warning' | 'error' = 'info', context?: any) {
    if (!this.isEnabled) return

    logger[level === 'warning' ? 'warn' : level]('error-tracking', message, context)

    // Send to error tracking service
    // Example: Sentry.captureMessage(message, level, { extra: context })
  }

  static setUserContext(user: { id: string; email?: string; name?: string }) {
    if (!this.isEnabled) return

    // Set user context for error tracking
    // Example: Sentry.setUser(user)
    logger.info('error-tracking', 'User context set', { userId: user.id })
  }
}

// Performance monitoring
export class PerformanceMonitor {
  private static marks: Map<string, number> = new Map()

  static mark(name: string) {
    this.marks.set(name, performance.now())
    logger.debug('performance', `Performance mark: ${name}`)
  }

  static measure(name: string, startMark?: string) {
    const endTime = performance.now()
    const startTime = startMark ? this.marks.get(startMark) : this.marks.get(name)

    if (startTime === undefined) {
      logger.warn('performance', `No start mark found for: ${name}`)
      return 0
    }

    const duration = endTime - startTime
    logger.info('performance', `Performance measure: ${name}`, { duration: `${duration.toFixed(2)}ms` })

    // Clean up the mark
    if (!startMark) {
      this.marks.delete(name)
    }

    return duration
  }

  static clearMarks() {
    this.marks.clear()
  }
}

// Debug utilities for development
export class DebugUtils {
  static logComponentRender(componentName: string, props?: any) {
    if (process.env.NODE_ENV !== 'development') return

    logger.debug('component', `Render: ${componentName}`, props ? { props } : undefined)
  }

  static logStateChange(componentName: string, oldState: any, newState: any) {
    if (process.env.NODE_ENV !== 'development') return

    logger.debug('component', `State change: ${componentName}`, {
      oldState,
      newState,
      diff: this.getStateDiff(oldState, newState)
    })
  }

  static logApiCall(method: string, url: string, data?: any) {
    if (process.env.NODE_ENV !== 'development') return

    logger.debug('api', `API Call: ${method} ${url}`, data ? { data } : undefined)
  }

  private static getStateDiff(oldState: any, newState: any): any {
    if (typeof oldState !== 'object' || typeof newState !== 'object') {
      return { old: oldState, new: newState }
    }

    const diff: any = {}
    const allKeys = new Set([...Object.keys(oldState || {}), ...Object.keys(newState || {})])

    for (const key of allKeys) {
      if (oldState?.[key] !== newState?.[key]) {
        diff[key] = { old: oldState?.[key], new: newState?.[key] }
      }
    }

    return diff
  }
}

// Memory usage monitoring
export class MemoryMonitor {
  static logMemoryUsage(context: string = 'general') {
    if (typeof window === 'undefined' || !('memory' in performance)) return

    const memory = (performance as any).memory
    logger.info('performance', `Memory usage: ${context}`, {
      used: `${Math.round(memory.usedJSHeapSize / 1024 / 1024)}MB`,
      total: `${Math.round(memory.totalJSHeapSize / 1024 / 1024)}MB`,
      limit: `${Math.round(memory.jsHeapSizeLimit / 1024 / 1024)}MB`,
    })
  }

  static startMemoryMonitoring(intervalMs: number = 30000) {
    if (typeof window === 'undefined') return

    const interval = setInterval(() => {
      this.logMemoryUsage('periodic')
    }, intervalMs)

    return () => clearInterval(interval)
  }
}

// Export types for use in other files
export type { LogContext, LogLevel }

