# 🚢 **SAILOR PLUS MARITIME PLATFORM - USER & ORGANIZATION FLOWS**

*Last Updated: June 2025*
*Status: Phase 5.4 Registration Flow Refactoring - COMPLETED*

---

## 📋 **OVERVIEW**

This document details the current user and organization interaction flows in the Sailor Plus Maritime Platform, as well as the intended future flows as the platform expands beyond certificates to include courses, job search, and crew management.

---

## 👤 **INDIVIDUAL USER FLOW**

### **CURRENT FLOW (Phase 5.4 - Implemented)**
```
User visits /signup
↓
Simplified registration form (name, email, password only)
↓
Email verification required (credential users)
↓
User redirected to /dashboard (personal workspace)
↓
Clean dashboard focused on personal certificates
↓
Optional: User can create organization via topbar context switcher
```

### **INTENDED FUTURE FLOW**
```
User visits /signup
↓
Simplified registration form (name, email, password only)
↓
Email verification required
↓
User redirected to /dashboard (personal workspace)
↓
Personal workspace with:
  - Certificate management
  - Course catalog browsing
  - Job search functionality
  - Career progression tracking
↓
Optional: User can create company/provider via settings (separate flow)
```

**Current Features:**
- ✅ Certificate upload, management, and download
- ✅ Multi-file support with preview capabilities
- ✅ Expiry date tracking
- ✅ Personal dashboard and profile management
- ✅ Email verification and account security

**Planned Features:**
- 🔄 Course catalog browsing and enrollment
- 🔄 Job search with filtering and alerts
- 🔄 Career progression tracking
- 🔄 Industry networking features
- 🔄 Mobile app for on-the-go access

---

## 🏢 **ORGANIZATION FLOW**

### **CURRENT FLOW (Phase 5.4 - Needs Improvement)**
```
Individual user in personal workspace
↓
Clicks organization context switcher in topbar
↓
Selects "Create Organization" from dropdown
↓
Visits /organizations/create
↓
Fills generic organization form (name, type, description, website)
↓
Organization created with "pending" status
↓
User becomes organization "owner"
↓
Admin verification required before full access
```

**CURRENT ISSUES:**
- ❌ Organization creation is in daily workflow (topbar dropdown)
- ❌ Generic form doesn't capture yacht/provider-specific details
- ❌ Unnecessary verification requirement for basic functionality
- ❌ No rich profiles for job search presentation

### **NEW INTENDED FLOW**
```
Individual user wants to create an organization
↓
Visits dedicated organization creation page (from settings)
↓
Sees organization type selection cards:
  - Yacht (with yacht icon and description)
  - Course Provider (with education icon and description)
  - [Future: Other maritime organizations]
↓
Selects organization type (e.g., "Yacht")
↓
Redirected to yacht-specific creation form with:
  - Basic info (name, description, location)
  - Yacht details (length, crew size, type, year built)
  - Media (photos, videos)
  - Features/amenities
  - Contact information
↓
Organization created immediately (no verification needed)
↓
User becomes organization "owner" with full access
↓
Optional verification later for:
  - Course providers: Public listing, certificate issuance
  - Yachts: Job market posting, enhanced visibility
```

**Organization Types & Workflows:**

#### **Yachts**
**Primary Use Cases:**
- Crew recruitment and management
- Job posting with yacht details and photos
- Crew certificate tracking and verification
- Voyage planning and crew scheduling
- Yacht profile for job market visibility

**Current Features:**
- ✅ Basic organization creation
- ✅ Multi-user organization management
- ✅ Role-based access control (owner/captain/crew)

**New Features Needed:**
- 🔄 Rich yacht profiles (photos, specifications, amenities)
- 🔄 Yacht-specific job posting templates
- 🔄 Crew management with position-specific requirements
- 🔄 Voyage tracking and crew scheduling
- 🔄 Integration with maritime job boards

**Yacht Profile Fields:**
- Basic: Name, description, home port, flag state
- Specifications: Length, beam, draft, gross tonnage, year built
- Type: Motor yacht, sailing yacht, expedition, charter, private
- Crew: Total crew size, positions available, experience requirements
- Media: Photos, videos, virtual tours
- Amenities: Features that make the yacht attractive to crew
- Contact: Preferred communication methods, recruitment preferences

#### **Course Providers / Training Organizations**
**Primary Use Cases:**
- Course content creation and delivery
- Student enrollment and management
- Certificate issuance and verification
- Training facility and accommodation management
- Revenue tracking and business analytics

**Current Features:**
- ✅ Basic organization creation
- ✅ Multi-user organization management
- ✅ Role-based access control

**New Features Needed:**
- 🔄 Course catalog with rich descriptions and media
- 🔄 Student enrollment and progress tracking
- 🔄 Certificate issuance workflow
- 🔄 Facility and accommodation booking integration
- 🔄 Revenue and analytics dashboard

**Course Provider Profile Fields:**
- Basic: Name, description, location, accreditations
- Courses: Course catalog, schedules, pricing, prerequisites
- Facilities: Training facilities, equipment, simulators
- Accommodation: On-site housing, nearby recommendations
- Instructors: Instructor profiles, qualifications, specializations
- Media: Facility photos, course videos, testimonials
- Certifications: Accrediting bodies, certificate types issued

---

## 🔄 **CONTEXT SWITCHING**

### **CURRENT IMPLEMENTATION**
The topbar contains an organization context switcher that allows users to:

1. **Personal Context**: Default view for individual certificates
2. **Organization Context(s)**: Switch to any organization the user belongs to
3. **Create Organization**: Currently available in dropdown (NEEDS TO BE REMOVED)

**Context Switcher Features:**
- ✅ Shows current context (Personal or Organization name)
- ✅ Lists all organizations user belongs to
- ✅ Shows organization status (verified, pending, suspended)
- ✅ Shows user's role in each organization (owner, admin, member)
- ✅ Quick switching between contexts
- ❌ Organization creation option (should be removed)

### **INTENDED FUTURE IMPLEMENTATION**
1. **Personal Context**: Individual maritime professional workspace
2. **Company Context(s)**: Yacht company workspaces with crew management features
3. **Provider Context(s)**: Training provider workspaces with course management features
4. **Clean Separation**: No creation options in switcher - purely for switching contexts

**Context-Aware Features:**
- **Certificates**: Personal vs organization certificates
- **Navigation**: Context-specific sidebar items
- **Permissions**: Role-based access within organizations
- **Data Isolation**: Complete separation between personal and organization data
- **Type-Specific Features**: Different dashboards for yacht companies vs course providers

---

## 👥 **MULTI-USER ORGANIZATION MANAGEMENT**

### **CURRENT ROLES (Implemented)**
1. **Owner**: Full control (created the organization)
   - ✅ Manage organization settings
   - ✅ Add/remove members
   - ✅ Manage certificates
   - ✅ Delete organization

2. **Admin**: Management permissions
   - ✅ Add/remove members
   - ✅ Manage certificates
   - ❌ Cannot delete organization

3. **Member**: View-only access
   - ✅ View organization certificates
   - ❌ Cannot manage members or settings

### **INTENDED FUTURE ROLES**
**For Yacht Companies:**
- **Owner**: Full yacht company control
- **Fleet Manager**: Manage multiple vessels and crews
- **HR Manager**: Crew recruitment and management
- **Compliance Officer**: Certificate verification and reporting
- **Crew Member**: Limited access to own records

**For Course Providers:**
- **Owner**: Full training organization control
- **Course Director**: Manage course content and delivery
- **Instructor**: Deliver courses and assess students
- **Administrator**: Student enrollment and logistics
- **Student**: Access to enrolled courses and certificates

### **MEMBERSHIP MANAGEMENT (Current)**
```
Organization owner/admin
↓
Invites user by email (when domain available)
↓
User receives invitation
↓
User accepts invitation
↓
User added to organization with specified role
↓
User can switch to organization context
```

---

## 🔐 **PERMISSION SYSTEM**

### **CURRENT PERMISSION MATRIX**
| Permission | Owner | Admin | Member |
|------------|-------|-------|--------|
| Manage Organization | ✅ | ❌ | ❌ |
| Manage Members | ✅ | ✅ | ❌ |
| Manage Certificates | ✅ | ✅ | ❌ |
| View Certificates | ✅ | ✅ | ✅ |
| Invite Members | ✅ | ✅ | ❌ |
| Delete Organization | ✅ | ❌ | ❌ |

### **INTENDED FUTURE PERMISSIONS**
**Yacht Company Permissions:**
- Crew Management, Job Posting, Certificate Verification, Compliance Reporting

**Course Provider Permissions:**
- Course Creation, Student Management, Certificate Issuance, Revenue Analytics

---

## 🎯 **USER EXPERIENCE PRINCIPLES**

### **CURRENT UX (Phase 5.4)**
- ✅ **Simple Registration**: No organization complexity during signup
- ✅ **Clean Dashboard**: Personal workspace is uncluttered
- ✅ **Optional Organizations**: Users can remain individual-only
- ❌ **Organization Creation**: Currently in topbar (needs to be moved)

### **INTENDED FUTURE UX**
- **Individual Focus**: Clean, simple experience for maritime professionals
- **Separate Business Flows**: Distinct signup and verification for businesses
- **Type-Specific Experiences**: Different workflows for yacht companies vs course providers
- **Context Clarity**: Always clear which workspace user is in
- **Progressive Disclosure**: Advanced features available when needed

---

## 🔄 **CURRENT VS INTENDED COMPARISON**

### **What's Working Well (Keep)**
- ✅ Simplified individual user registration
- ✅ Clean personal workspace
- ✅ Context switching between personal and organization views
- ✅ Role-based permissions within organizations
- ✅ Admin verification workflow

### **What Needs Improvement (Change)**
- ❌ Organization creation in topbar dropdown → Move to separate flow
- ❌ "Organization" terminology → Use "Company"/"Provider"
- ❌ Same flow for all business types → Type-specific flows
- ❌ Certificate-only focus → Multi-workflow platform

### **What's Missing (Add)**
- 🔄 Course management system
- 🔄 Job posting and search functionality
- 🔄 Crew management tools
- 🔄 Type-specific dashboards and navigation
- 🔄 Student enrollment and progress tracking

---

## 🚀 **IMPLEMENTATION ROADMAP**

### **Phase 1: Navigation & Flow Cleanup (Immediate)**
1. Remove organization creation from topbar context switcher
2. Create separate business signup flow
3. Update terminology throughout application
4. Implement type-specific registration forms

### **Phase 2: Platform Expansion (Short-term)**
1. Add course management database schema
2. Implement job posting functionality
3. Create type-specific dashboards
4. Build crew management tools

### **Phase 3: Advanced Features (Medium-term)**
1. Course content delivery system
2. Advanced search and matching algorithms
3. Mobile application development
4. Integration with external systems

**This document provides a comprehensive overview of current user flows and the intended future state as Sailor Plus evolves into a comprehensive maritime platform serving individual professionals, yacht companies, and training providers.**
