import { neon } from "@neondatabase/serverless";
import { and, eq, like } from "drizzle-orm";
import { drizzle } from "drizzle-orm/neon-http";
import { pgTable, text } from "drizzle-orm/pg-core";
import { NextRequest, NextResponse } from "next/server";

// Define schema tables for cleanup
const users = pgTable("User", {
  id: text("id").primaryKey(),
  email: text("email").notNull().unique(),
});

const certificates = pgTable("Certificate", {
  id: text("id").primaryKey(),
  name: text("name").notNull(),
  userId: text("userId").notNull(),
});

const notifications = pgTable("Notification", {
  id: text("id").primaryKey(),
  userId: text("userId").notNull(),
});

const organizations = pgTable("Organization", {
  id: text("id").primary<PERSON>ey(),
  name: text("name").notNull(),
});

const organizationMemberships = pgTable("OrganizationMembership", {
  id: text("id").primaryKey(),
  userId: text("userId").notNull(),
  organizationId: text("organizationId").notNull(),
});

/**
 * Clean up test data for E2E testing
 * POST /api/admin/cleanup-test-data
 */
export async function POST(request: NextRequest) {
  try {
    // Only allow in development/test environments
    if (process.env.NODE_ENV === "production") {
      return NextResponse.json(
        { error: "Test data cleanup is disabled in production" },
        { status: 403 }
      );
    }

    if (!process.env.DATABASE_URL) {
      return NextResponse.json(
        { error: "DATABASE_URL environment variable is required" },
        { status: 500 }
      );
    }

    const sql = neon(process.env.DATABASE_URL);
    const db = drizzle(sql);

    const body = await request.json();
    const { action = 'cleanup_all', targetUser = '<EMAIL>' } = body;

    const cleanupResults = {
      certificatesDeleted: 0,
      notificationsDeleted: 0,
      organizationsDeleted: 0,
      membershipsDeleted: 0,
    };

    // Get the target user
    const targetUserRecord = await db
      .select()
      .from(users)
      .where(eq(users.email, targetUser))
      .limit(1);

    if (targetUserRecord.length === 0) {
      return NextResponse.json(
        { error: `Target user ${targetUser} not found` },
        { status: 404 }
      );
    }

    const targetUserId = targetUserRecord[0].id;

    switch (action) {
      case 'cleanup_all':
        // Clean up certificates for target user
        const deletedCertificates = await db
          .delete(certificates)
          .where(eq(certificates.userId, targetUserId));
        cleanupResults.certificatesDeleted = deletedCertificates.rowCount || 0;

        // Clean up notifications for target user
        const deletedNotifications = await db
          .delete(notifications)
          .where(eq(notifications.userId, targetUserId));
        cleanupResults.notificationsDeleted = deletedNotifications.rowCount || 0;

        // Clean up test organizations (those with "Test" or "E2E" in name)
        const testOrganizations = await db
          .select()
          .from(organizations)
          .where(
            and(
              like(organizations.name, '%Test%'),
              like(organizations.name, '%E2E%')
            )
          );

        for (const org of testOrganizations) {
          // Delete memberships first
          const deletedMemberships = await db
            .delete(organizationMemberships)
            .where(eq(organizationMemberships.organizationId, org.id));
          cleanupResults.membershipsDeleted += deletedMemberships.rowCount || 0;

          // Delete organization
          await db
            .delete(organizations)
            .where(eq(organizations.id, org.id));
          cleanupResults.organizationsDeleted++;
        }
        break;

      case 'cleanup_certificates':
        const deletedCerts = await db
          .delete(certificates)
          .where(eq(certificates.userId, targetUserId));
        cleanupResults.certificatesDeleted = deletedCerts.rowCount || 0;
        break;

      case 'cleanup_notifications':
        const deletedNotifs = await db
          .delete(notifications)
          .where(eq(notifications.userId, targetUserId));
        cleanupResults.notificationsDeleted = deletedNotifs.rowCount || 0;
        break;

      case 'cleanup_organizations':
        // Only clean up test organizations
        const testOrgs = await db
          .select()
          .from(organizations)
          .where(
            and(
              like(organizations.name, '%Test%'),
              like(organizations.name, '%E2E%')
            )
          );

        for (const org of testOrgs) {
          // Delete memberships first
          const deletedMemberships = await db
            .delete(organizationMemberships)
            .where(eq(organizationMemberships.organizationId, org.id));
          cleanupResults.membershipsDeleted += deletedMemberships.rowCount || 0;

          // Delete organization
          await db
            .delete(organizations)
            .where(eq(organizations.id, org.id));
          cleanupResults.organizationsDeleted++;
        }
        break;

      default:
        return NextResponse.json(
          { error: "Invalid action. Use 'cleanup_all', 'cleanup_certificates', 'cleanup_notifications', or 'cleanup_organizations'" },
          { status: 400 }
        );
    }

    return NextResponse.json({
      success: true,
      message: `Test data cleanup completed for ${targetUser}`,
      action,
      results: cleanupResults,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error("Error cleaning up test data:", error);
    return NextResponse.json(
      { error: "Failed to cleanup test data", details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
}

/**
 * Get test data status
 * GET /api/admin/cleanup-test-data
 */
export async function GET(request: NextRequest) {
  try {
    if (process.env.NODE_ENV === "production") {
      return NextResponse.json(
        { error: "Test data endpoints are disabled in production" },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const targetUser = searchParams.get('user') || '<EMAIL>';

    const sql = neon(process.env.DATABASE_URL!);
    const db = drizzle(sql);

    // Get the target user
    const targetUserRecord = await db
      .select()
      .from(users)
      .where(eq(users.email, targetUser))
      .limit(1);

    if (targetUserRecord.length === 0) {
      return NextResponse.json({
        success: false,
        message: `Target user ${targetUser} not found`,
      });
    }

    const targetUserId = targetUserRecord[0].id;

    // Count current test data
    const certificateCount = await db
      .select()
      .from(certificates)
      .where(eq(certificates.userId, targetUserId));

    const notificationCount = await db
      .select()
      .from(notifications)
      .where(eq(notifications.userId, targetUserId));

    const testOrganizations = await db
      .select()
      .from(organizations)
      .where(
        and(
          like(organizations.name, '%Test%'),
          like(organizations.name, '%E2E%')
        )
      );

    return NextResponse.json({
      success: true,
      data: {
        targetUser,
        targetUserId,
        currentData: {
          certificates: certificateCount.length,
          notifications: notificationCount.length,
          testOrganizations: testOrganizations.length,
        },
        lastChecked: new Date().toISOString(),
      },
    });

  } catch (error) {
    console.error("Error getting test data status:", error);
    return NextResponse.json(
      { error: "Failed to get test data status", details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
}
