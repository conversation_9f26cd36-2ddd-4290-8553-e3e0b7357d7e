import { expect, test } from '@playwright/test';

test.describe('Organization Management - Core Functionality', () => {
  // Use the saved authentication state
  test.use({ storageState: 'e2e/auth-state.json' });

  test.beforeEach(async ({ page }) => {
    // Clean up any existing test organizations
    await page.request.post('/api/admin/cleanup-test-data', {
      headers: { 'Content-Type': 'application/json' },
      data: { action: 'cleanup_organizations' }
    });
  });

  test.afterEach(async ({ page }) => {
    // Clean up test organizations after each test
    await page.request.post('/api/admin/cleanup-test-data', {
      headers: { 'Content-Type': 'application/json' },
      data: { action: 'cleanup_organizations' }
    });
  });

  test('should display organization selection page correctly', async ({ page }) => {
    await page.goto('/organizations/new');

    // Check if organization selection page loads
    await expect(page.locator('h1')).toContainText('Create Your Organization');

    // Check for organization type cards (using specific test IDs)
    await expect(page.locator('[data-testid="yacht-card-title"]')).toBeVisible();
    await expect(page.locator('[data-testid="training-provider-card-title"]')).toBeVisible();

    // Check for yacht features (using actual text)
    await expect(page.locator('text=Post job openings with yacht details')).toBeVisible();
    await expect(page.locator('text=Manage crew certificates and documents')).toBeVisible();

    // Check for training provider features (using actual text)
    await expect(page.locator('text=Create and manage course catalogs')).toBeVisible();
    await expect(page.locator('text=Issue certificates and credentials')).toBeVisible();

    console.log('✅ Organization selection page loads correctly');
  });

  // TODO: Re-enable when organization creation logic is finalized
  test.skip('should create yacht company and verify dashboard', async ({ page }) => {
    // Test skipped - organization creation logic is being redesigned
    console.log('⏭️ Yacht company creation test skipped - feature under development');
  });

  test('should create training provider and verify dashboard', async ({ page }) => {
    await page.goto('/organizations/new');

    // Select training provider
    await page.click('[data-testid="create-training-provider-button"]');
    await expect(page).toHaveURL('/organizations/create/training-provider');

    // Verify training provider creation form loads (using actual title)
    await expect(page.locator('h1')).toContainText('Create Training Provider');
    await expect(page.locator('input[name="name"]')).toBeVisible();

    // Fill out training provider form
    const providerName = 'Test Training Provider E2E';
    await page.fill('input[name="name"]', providerName);
    await page.fill('textarea[name="description"]', 'Test training provider for E2E testing');
    await page.fill('input[name="website"]', 'https://testtraining.example.com');

    // Submit form
    await page.click('button[type="submit"]');

    // Wait for success message or redirect
    await page.waitForTimeout(3000);

    // Should redirect to dashboard with success message
    await expect(page).toHaveURL('/dashboard');

    // Look for success message
    const successMessage = page.locator('text=Organization created successfully').or(
      page.locator('text=Business account created').or(
        page.locator('[role="alert"]')
      )
    );

    const successCount = await successMessage.count();
    if (successCount > 0) {
      await expect(successMessage.first()).toBeVisible();
      console.log('✅ Training provider creation success message displayed');
    }

    console.log('✅ Training provider creation flow completed');
  });

  test('should handle organization creation form validation', async ({ page }) => {
    await page.goto('/organizations/create/yacht');

    // Try to submit empty form
    await page.click('button[type="submit"]');
    await page.waitForTimeout(1000);

    // Check for validation errors
    const nameField = page.locator('input[name="name"]');
    const isInvalid = await nameField.evaluate((el: HTMLInputElement) => !el.validity.valid);

    if (isInvalid) {
      console.log('✅ Empty form submission prevented by validation');
    }

    // Fill in minimum required fields
    await page.fill('input[name="name"]', 'Valid Organization Name');

    // Check that form is now valid
    const isValid = await nameField.evaluate((el: HTMLInputElement) => el.validity.valid);

    if (isValid) {
      console.log('✅ Form validation working correctly');
    }

    console.log('✅ Organization form validation tested');
  });

  test('should be mobile responsive', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });

    await page.goto('/organizations/new');

    // Check if page is responsive
    await expect(page.locator('h1')).toBeVisible();

    // Check if organization cards are still visible and clickable (using test IDs)
    await expect(page.locator('[data-testid="yacht-card-title"]')).toBeVisible();
    await expect(page.locator('[data-testid="training-provider-card-title"]')).toBeVisible();

    // Test navigation on mobile
    await page.click('[data-testid="create-yacht-button"]');
    await expect(page).toHaveURL('/organizations/create/yacht');

    // Check if form is responsive
    await expect(page.locator('input[name="name"]')).toBeVisible();

    console.log('✅ Organization pages are mobile responsive');
  });

  // TODO: Re-enable when organization navigation is finalized
  test.skip('should navigate back to dashboard from organization creation', async ({ page }) => {
    // Test skipped - organization navigation logic is being redesigned
    console.log('⏭️ Organization navigation test skipped - feature under development');
  });
});
