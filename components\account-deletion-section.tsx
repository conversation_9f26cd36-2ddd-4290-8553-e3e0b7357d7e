"use client";

import {
  <PERSON>ertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { AlertTriangle, Clock, RefreshCw, Shield, Trash2 } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";

interface AccountDeletionSectionProps {
  userEmail: string;
  userName: string;
}

type DeletionStatus = {
  status: "deletion_requested" | "soft_deleted";
  requestedAt: string;
  recoveryExpiresAt: string;
  deletedAt: string;
  permanentDeletionDate: string;
  reason?: string;
} | null;

export function AccountDeletionSection({
  userEmail,
}: AccountDeletionSectionProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [confirmEmail, setConfirmEmail] = useState("");
  const [reason, setReason] = useState("");
  const [immediateDelete, setImmediateDelete] = useState(false);
  const [understandConsequences, setUnderstandConsequences] = useState(false);
  const [deletionStatus, setDeletionStatus] = useState<DeletionStatus>(null);

  // Check deletion status on component mount
  useState(() => {
    checkDeletionStatus();
  });

  const checkDeletionStatus = async () => {
    try {
      const response = await fetch("/api/account/delete", {
        method: "GET",
        credentials: "include",
      });

      if (response.ok) {
        const data = await response.json();
        setDeletionStatus(data.status);
      }
    } catch (error) {
      console.error("Failed to check deletion status:", error);
    }
  };

  const handleDeleteAccount = async () => {
    if (!confirmEmail || confirmEmail !== userEmail) {
      toast.error("Please confirm your email address correctly");
      return;
    }

    if (!understandConsequences) {
      toast.error("Please confirm that you understand the consequences");
      return;
    }

    setIsLoading(true);

    try {
      const response = await fetch("/api/account/delete", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include",
        body: JSON.stringify({
          confirmEmail,
          reason: reason.trim() || undefined,
          immediateDelete,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        if (data.type === "immediate") {
          toast.success(
            "Account deleted successfully. You will be logged out shortly."
          );
          // Redirect to logout after a short delay
          setTimeout(() => {
            window.location.href = "/api/logout";
          }, 2000);
        } else {
          toast.success(
            "Account deletion scheduled. Check your email for recovery instructions."
          );
          // Refresh the deletion status
          await checkDeletionStatus();
        }
      } else {
        toast.error(data.error || "Failed to delete account");
      }
    } catch (error) {
      console.error("Account deletion error:", error);
      toast.error("Failed to delete account. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleRecoverAccount = async () => {
    // This would typically be handled by a recovery link in email
    // For now, we&apos;ll just refresh the status
    await checkDeletionStatus();
  };

  // Show recovery interface if account is scheduled for deletion
  if (deletionStatus?.status === "deletion_requested") {
    return (
      <Card className="border-amber-200 bg-amber-50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-amber-800">
            <Clock className="h-5 w-5" />
            Account Deletion Scheduled
          </CardTitle>
          <CardDescription className="text-amber-700">
            Your account is scheduled for deletion. You can still recover it.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="bg-white p-4 rounded-lg border border-amber-200">
            <h4 className="font-medium text-amber-800 mb-2">
              Deletion Details:
            </h4>
            <ul className="text-sm text-amber-700 space-y-1">
              <li>
                • Requested:{" "}
                {new Date(deletionStatus.requestedAt).toLocaleDateString()}
              </li>
              <li>
                • Recovery expires:{" "}
                {new Date(
                  deletionStatus.recoveryExpiresAt
                ).toLocaleDateString()}
              </li>
              {deletionStatus.reason && (
                <li>• Reason: {deletionStatus.reason}</li>
              )}
            </ul>
          </div>

          <div className="flex gap-2">
            <Button
              onClick={handleRecoverAccount}
              variant="default"
              className="bg-green-600 hover:bg-green-700"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Recover Account
            </Button>
            <Button onClick={checkDeletionStatus} variant="outline">
              Refresh Status
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Show soft deleted status
  if (deletionStatus?.status === "soft_deleted") {
    return (
      <Card className="border-red-200 bg-red-50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-red-800">
            <Trash2 className="h-5 w-5" />
            Account Deleted
          </CardTitle>
          <CardDescription className="text-red-700">
            Your account has been deleted and data anonymized.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="bg-white p-4 rounded-lg border border-red-200">
            <p className="text-sm text-red-700">
              Deleted: {new Date(deletionStatus.deletedAt).toLocaleDateString()}
            </p>
            <p className="text-sm text-red-700 mt-2">
              Permanent deletion:{" "}
              {new Date(
                deletionStatus.permanentDeletionDate
              ).toLocaleDateString()}
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Show normal deletion interface
  return (
    <Card className="border-red-200">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-red-600">
          <AlertTriangle className="h-5 w-5" />
          Delete Account
        </CardTitle>
        <CardDescription>
          Permanently delete your account and all associated data. This action
          cannot be undone.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <h4 className="font-medium text-red-800 mb-2">
            ⚠️ What will be deleted:
          </h4>
          <ul className="text-sm text-red-700 space-y-1">
            <li>• Your profile and account information</li>
            <li>• All certificates and uploaded files</li>
            <li>• Account settings and preferences</li>
            <li>• Login credentials and linked social accounts</li>
          </ul>
        </div>

        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="reason">Reason for deletion (optional)</Label>
            <Textarea
              id="reason"
              placeholder="Help us improve by telling us why you're leaving..."
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              className="min-h-[80px]"
            />
          </div>

          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="immediate"
                checked={immediateDelete}
                onCheckedChange={(checked) =>
                  setImmediateDelete(checked as boolean)
                }
              />
              <Label htmlFor="immediate" className="text-sm">
                Delete immediately (no 30-day recovery period)
              </Label>
            </div>

            {!immediateDelete && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                <div className="flex items-center gap-2 text-blue-800 mb-1">
                  <Shield className="h-4 w-4" />
                  <span className="font-medium">30-Day Recovery Period</span>
                </div>
                <p className="text-sm text-blue-700">
                  Your account will be scheduled for deletion but you&apos;ll
                  have 30 days to recover it if you change your mind.
                </p>
              </div>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="confirmEmail">Confirm your email address</Label>
            <Input
              id="confirmEmail"
              type="email"
              placeholder={userEmail}
              value={confirmEmail}
              onChange={(e) => setConfirmEmail(e.target.value)}
            />
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="understand"
              checked={understandConsequences}
              onCheckedChange={(checked) =>
                setUnderstandConsequences(checked as boolean)
              }
            />
            <Label htmlFor="understand" className="text-sm">
              I understand that this action is permanent and cannot be undone
            </Label>
          </div>
        </div>

        <AlertDialog>
          <AlertDialogTrigger asChild>
            <Button
              variant="destructive"
              disabled={!confirmEmail || !understandConsequences || isLoading}
              className="w-full"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              {isLoading ? "Deleting Account..." : "Delete My Account"}
            </Button>
          </AlertDialogTrigger>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
              <AlertDialogDescription>
                This will{" "}
                {immediateDelete
                  ? "immediately and permanently"
                  : "schedule your account for"}{" "}
                deletion.
                {!immediateDelete &&
                  " You will have 30 days to recover your account."}
                All your certificates, files, and personal data will be removed
                from our systems.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={handleDeleteAccount}
                className="bg-red-600 hover:bg-red-700"
              >
                Yes, Delete My Account
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </CardContent>
    </Card>
  );
}
