"use client";

import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  Toolt<PERSON>Provider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  AlertCircle,
  Eye,
  File,
  FileText,
  Image,
  Move,
  Plus,
  Upload,
  X,
} from "lucide-react";
import { useCallback, useRef, useState } from "react";

export interface LocalFile {
  id: string;
  file: File;
  name: string;
  size: number;
  type: string;
  preview?: string;
  lastModified: number;
}

interface LocalFileUploadProps {
  onFilesChange: (files: LocalFile[]) => void;
  maxFiles?: number;
  maxFileSize?: number; // in bytes
  acceptedTypes?: string[];
  disabled?: boolean;
  className?: string;
}

export function LocalFileUpload({
  onFilesChange,
  maxFiles = 5,
  maxFileSize = 8 * 1024 * 1024, // 8MB
  acceptedTypes = ["application/pdf", "image/jpeg", "image/png"],
  disabled = false,
  className = "",
}: LocalFileUploadProps) {
  const [localFiles, setLocalFiles] = useState<LocalFile[]>([]);
  const [dragActive, setDragActive] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const getFileIcon = (type: string) => {
    if (type.includes("pdf"))
      return <FileText className="h-5 w-5 text-red-500" />;
    if (type.includes("image"))
      return <Image className="h-5 w-5 text-blue-500" />;
    return <File className="h-5 w-5 text-gray-500" />;
  };

  const generateFileId = () => {
    return `file_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  };

  const createFilePreview = (file: File): Promise<string | undefined> => {
    return new Promise((resolve) => {
      if (file.type.startsWith("image/")) {
        const reader = new FileReader();
        reader.onload = (e) => resolve(e.target?.result as string);
        reader.onerror = () => resolve(undefined);
        reader.readAsDataURL(file);
      } else {
        resolve(undefined);
      }
    });
  };

  const validateFile = (file: File): string | null => {
    if (!acceptedTypes.includes(file.type)) {
      return `File type ${
        file.type
      } is not supported. Accepted types: ${acceptedTypes.join(", ")}`;
    }
    if (file.size > maxFileSize) {
      return `File size ${formatFileSize(
        file.size
      )} exceeds maximum size of ${formatFileSize(maxFileSize)}`;
    }
    return null;
  };

  const processFiles = useCallback(
    async (files: FileList) => {
      setError(null);
      const newFiles: LocalFile[] = [];
      const errors: string[] = [];

      for (let i = 0; i < files.length; i++) {
        const file = files[i];

        // Check if we've reached the max files limit
        if (localFiles.length + newFiles.length >= maxFiles) {
          errors.push(`Maximum ${maxFiles} files allowed`);
          break;
        }

        // Validate file
        const validationError = validateFile(file);
        if (validationError) {
          errors.push(`${file.name}: ${validationError}`);
          continue;
        }

        // Check for duplicates
        const isDuplicate = localFiles.some(
          (existingFile) =>
            existingFile.name === file.name &&
            existingFile.size === file.size &&
            existingFile.lastModified === file.lastModified
        );

        if (isDuplicate) {
          errors.push(`${file.name}: File already added`);
          continue;
        }

        // Create preview for images
        const preview = await createFilePreview(file);

        const localFile: LocalFile = {
          id: generateFileId(),
          file,
          name: file.name,
          size: file.size,
          type: file.type,
          preview,
          lastModified: file.lastModified,
        };

        newFiles.push(localFile);
      }

      if (errors.length > 0) {
        setError(errors.join("; "));
      }

      if (newFiles.length > 0) {
        const updatedFiles = [...localFiles, ...newFiles];
        setLocalFiles(updatedFiles);
        onFilesChange(updatedFiles);
      }
    },
    [localFiles, maxFiles, maxFileSize, acceptedTypes, onFilesChange]
  );

  const handleFileSelect = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const files = e.target.files;
      if (files && files.length > 0) {
        processFiles(files);
      }
      // Reset input value to allow selecting the same file again
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    },
    [processFiles]
  );

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  }, []);

  const handleDragIn = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.dataTransfer.items && e.dataTransfer.items.length > 0) {
      setDragActive(true);
    }
  }, []);

  const handleDragOut = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
  }, []);

  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      e.stopPropagation();
      setDragActive(false);

      if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
        processFiles(e.dataTransfer.files);
      }
    },
    [processFiles]
  );

  const removeFile = (fileId: string) => {
    const updatedFiles = localFiles.filter((file) => file.id !== fileId);
    setLocalFiles(updatedFiles);
    onFilesChange(updatedFiles);
    setError(null);
  };

  const moveFile = (fileId: string, direction: "up" | "down") => {
    const currentIndex = localFiles.findIndex((file) => file.id === fileId);
    if (currentIndex === -1) return;

    const newIndex = direction === "up" ? currentIndex - 1 : currentIndex + 1;
    if (newIndex < 0 || newIndex >= localFiles.length) return;

    const updatedFiles = [...localFiles];
    [updatedFiles[currentIndex], updatedFiles[newIndex]] = [
      updatedFiles[newIndex],
      updatedFiles[currentIndex],
    ];

    setLocalFiles(updatedFiles);
    onFilesChange(updatedFiles);
  };

  const openFileDialog = () => {
    fileInputRef.current?.click();
  };

  const canAddMore = localFiles.length < maxFiles;

  return (
    <TooltipProvider>
      <div className={`space-y-4 ${className}`}>
        {/* Upload Area */}
        {canAddMore && !disabled && (
          <div
            className={`
              relative border-2 border-dashed rounded-lg p-4 text-center transition-all duration-200 cursor-pointer
              ${
                dragActive
                  ? "border-primary bg-primary/5 scale-[1.02]"
                  : "border-border hover:border-primary/50 hover:bg-muted/30"
              }
              ${disabled ? "opacity-50 cursor-not-allowed" : ""}
            `}
            onDragEnter={handleDragIn}
            onDragLeave={handleDragOut}
            onDragOver={handleDrag}
            onDrop={handleDrop}
            onClick={openFileDialog}
            title="Click to select multiple files or drag and drop them here"
          >
            <input
              ref={fileInputRef}
              type="file"
              multiple
              accept={acceptedTypes.join(",")}
              onChange={handleFileSelect}
              className="hidden"
              disabled={disabled}
              aria-label="Select multiple certificate files"
            />

            <div className="flex flex-col items-center space-y-3">
              <div
                className={`
                p-3 rounded-full transition-colors duration-200
                ${
                  dragActive
                    ? "bg-primary text-primary-foreground"
                    : "bg-muted text-muted-foreground"
                }
              `}
              >
                <Upload className="h-6 w-6" />
              </div>

              <div className="space-y-1">
                <h3 className="text-base font-medium">
                  {dragActive ? "Drop files here" : "Upload Documents"}
                </h3>
                <p className="text-xs text-muted-foreground">
                  PDF, JPG, PNG • Max {formatFileSize(maxFileSize)} • Up to{" "}
                  {maxFiles} files
                </p>
              </div>

              <Button
                variant="outline"
                size="sm"
                type="button"
                className="h-8 text-xs"
              >
                <Plus className="h-3 w-3 mr-1" />
                Select Multiple Files
              </Button>
            </div>
          </div>
        )}

        {/* Error Display */}
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Files List */}
        {localFiles.length > 0 && (
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <h4 className="text-sm font-medium">Selected Files</h4>
              <Badge variant="secondary" className="text-xs">
                {localFiles.length} of {maxFiles}
              </Badge>
            </div>

            <div className="space-y-2">
              {localFiles.map((file, index) => (
                <Card key={file.id} className="p-0 overflow-hidden">
                  <CardContent className="p-4">
                    <div className="flex items-center space-x-4">
                      {/* File Icon/Preview */}
                      <div className="flex-shrink-0">
                        {file.preview ? (
                          <div className="relative">
                            <img
                              src={file.preview}
                              alt={file.name}
                              className="h-12 w-12 object-cover rounded border"
                            />
                            <Button
                              variant="ghost"
                              size="sm"
                              className="absolute -top-1 -right-1 h-6 w-6 p-0 bg-background border"
                              onClick={() =>
                                window.open(file.preview, "_blank")
                              }
                            >
                              <Eye className="h-3 w-3" />
                            </Button>
                          </div>
                        ) : (
                          <div className="h-12 w-12 flex items-center justify-center bg-muted rounded border">
                            {getFileIcon(file.type)}
                          </div>
                        )}
                      </div>

                      {/* File Info */}
                      <div className="flex-1 min-w-0">
                        <p
                          className="text-sm font-medium truncate"
                          title={file.name}
                        >
                          {file.name}
                        </p>
                        <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                          <span>{formatFileSize(file.size)}</span>
                          <span>•</span>
                          <span className="capitalize">
                            {file.type.split("/")[1] || "Unknown"}
                          </span>
                        </div>
                      </div>

                      {/* Actions */}
                      <div className="flex items-center space-x-1">
                        {localFiles.length > 1 && (
                          <>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => moveFile(file.id, "up")}
                                  disabled={index === 0 || disabled}
                                  className="h-8 w-8 p-0"
                                >
                                  <Move className="h-4 w-4 rotate-180" />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>Move up</p>
                              </TooltipContent>
                            </Tooltip>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => moveFile(file.id, "down")}
                                  disabled={
                                    index === localFiles.length - 1 || disabled
                                  }
                                  className="h-8 w-8 p-0"
                                >
                                  <Move className="h-4 w-4" />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>Move down</p>
                              </TooltipContent>
                            </Tooltip>
                          </>
                        )}
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => removeFile(file.id)}
                              disabled={disabled}
                              className="h-8 w-8 p-0 text-destructive hover:text-destructive"
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Remove file</p>
                          </TooltipContent>
                        </Tooltip>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        )}

        {/* Upload Limit Reached */}
        {!canAddMore && !disabled && (
          <Alert>
            <Upload className="h-4 w-4" />
            <AlertDescription>
              Maximum number of files ({maxFiles}) reached. Remove a file to add
              another.
            </AlertDescription>
          </Alert>
        )}
      </div>
    </TooltipProvider>
  );
}
