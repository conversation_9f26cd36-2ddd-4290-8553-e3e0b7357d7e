"use client";

import { <PERSON><PERSON>, AlertDescription } from "@/components/ui/alert";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { handleApiError } from "@/lib/auth-utils";
import { logger } from "@/lib/logger";
import {
  AlertTriangle,
  CheckCircle,
  Loader2,
  Mail,
  RefreshCw,
} from "lucide-react";
import { useRouter } from "next/navigation";
import { useCallback, useEffect, useRef, useState } from "react";

interface UserSession {
  userId: string;
  email: string;
  name: string;
  emailVerified: boolean;
}

// Simple loading skeleton
const LoadingSkeleton = () => (
  <div className="container flex items-center justify-center min-h-[calc(100vh-16rem)] py-12">
    <Card className="mx-auto max-w-sm w-full">
      <CardHeader className="space-y-1">
        <div className="flex justify-center mb-2">
          <Skeleton className="h-10 w-10 rounded-full" />
        </div>
        <Skeleton className="h-6 w-3/4 mx-auto" />
        <Skeleton className="h-4 w-full" />
      </CardHeader>
      <CardContent className="space-y-4">
        <Skeleton className="h-16 w-full" />
        <Skeleton className="h-8 w-full" />
      </CardContent>
      <CardFooter className="space-y-2">
        <Skeleton className="h-10 w-full" />
        <Skeleton className="h-10 w-full" />
      </CardFooter>
    </Card>
  </div>
);

export default function VerificationPendingPage() {
  const router = useRouter();
  const [session, setSession] = useState<UserSession | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isResending, setIsResending] = useState(false);
  const [resendMessage, setResendMessage] = useState<string | null>(null);
  const [resendSuccess, setResendSuccess] = useState(false);
  const [lastResendTime, setLastResendTime] = useState<number | null>(null);
  const [resendCooldown, setResendCooldown] = useState(0);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const cooldownRef = useRef<NodeJS.Timeout | null>(null);

  // Rate limiting: 60 seconds between resend attempts
  const RESEND_COOLDOWN_SECONDS = 60;
  // Auto-check verification status every 10 seconds
  const VERIFICATION_CHECK_INTERVAL = 10000;

  // Enhanced session check with automatic verification status polling
  const checkVerificationStatus = useCallback(
    async (isInitial = false) => {
      const endTimer = logger.authStart(
        isInitial ? "verification-session-check" : "verification-status-poll",
        "unknown"
      );

      try {
        const response = await fetch("/api/auth/session", {
          credentials: "include",
        });

        if (response.ok) {
          const data = await response.json();
          if (data.session) {
            setSession(data.session);

            // Enhanced logging for debugging
            console.log(`[Verification Polling] Session check result:`, {
              userId: data.session.userId,
              email: data.session.email,
              emailVerified: data.session.emailVerified,
              source: data.source,
              isInitial,
            });

            // Immediate redirect if verified
            if (data.session.emailVerified) {
              console.log(
                `[Verification Polling] ✅ Email verified! Redirecting to dashboard...`
              );
              logger.authSuccess(
                "email-verification-complete",
                data.session.userId
              );

              // Clear any running intervals
              if (intervalRef.current) {
                clearInterval(intervalRef.current);
                intervalRef.current = null;
              }

              router.push("/dashboard");
              return true; // Verification complete
            } else {
              console.log(
                `[Verification Polling] ⏳ Still waiting for verification...`
              );
            }
          } else {
            logger.authFailure(
              "verification-session-check",
              "No session found",
              "unknown"
            );
            router.push("/login");
            return false;
          }
        } else {
          logger.authFailure(
            "verification-session-check",
            `HTTP ${response.status}`,
            "unknown"
          );
          router.push("/login");
          return false;
        }
      } catch (error) {
        const errorMessage = handleApiError(error, "Session check failed");
        logger.error("auth", "Verification session check error", {
          error: errorMessage,
        });

        if (isInitial) {
          router.push("/login");
          return false;
        }
        // For polling errors, just log and continue
      } finally {
        if (isInitial) {
          setIsLoading(false);
        }
        endTimer();
      }

      return false; // Not verified yet
    },
    [router]
  );

  // Initial session check and setup polling
  useEffect(() => {
    checkVerificationStatus(true);

    // Set up automatic verification status checking
    intervalRef.current = setInterval(() => {
      checkVerificationStatus(false);
    }, VERIFICATION_CHECK_INTERVAL);

    // Cleanup on unmount
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
      if (cooldownRef.current) {
        clearTimeout(cooldownRef.current);
        cooldownRef.current = null;
      }
    };
  }, [checkVerificationStatus, VERIFICATION_CHECK_INTERVAL]);

  // Enhanced resend verification with rate limiting
  const handleResendVerification = async () => {
    if (!session?.email) return;

    // Check rate limiting
    const now = Date.now();
    if (
      lastResendTime &&
      now - lastResendTime < RESEND_COOLDOWN_SECONDS * 1000
    ) {
      const remainingSeconds = Math.ceil(
        (RESEND_COOLDOWN_SECONDS * 1000 - (now - lastResendTime)) / 1000
      );
      setResendMessage(
        `Please wait ${remainingSeconds} seconds before requesting another email.`
      );
      setResendSuccess(false);
      return;
    }

    setIsResending(true);
    setResendMessage(null);
    setResendSuccess(false);

    const endTimer = logger.authStart("resend-verification", session.email);

    try {
      const response = await fetch("/api/auth/resend-verification", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email: session.email }),
      });

      const data = await response.json();

      if (data.success) {
        logger.authSuccess("resend-verification", session.userId);
        setResendMessage("Verification email sent! Please check your inbox.");
        setResendSuccess(true);

        // Set rate limiting
        setLastResendTime(now);
        setResendCooldown(RESEND_COOLDOWN_SECONDS);

        // Start cooldown timer
        cooldownRef.current = setInterval(() => {
          setResendCooldown((prev) => {
            if (prev <= 1) {
              if (cooldownRef.current) {
                clearInterval(cooldownRef.current);
                cooldownRef.current = null;
              }
              return 0;
            }
            return prev - 1;
          });
        }, 1000);
      } else {
        const errorMessage =
          data.error || "Failed to resend verification email.";
        logger.authFailure("resend-verification", errorMessage, session.email);
        setResendMessage(errorMessage);
        setResendSuccess(false);
      }
    } catch (error) {
      const errorMessage = handleApiError(
        error,
        "Failed to resend verification email. Please try again."
      );
      logger.error("auth", "Resend verification error", {
        error: errorMessage,
      });
      setResendMessage(errorMessage);
      setResendSuccess(false);
    } finally {
      setIsResending(false);
      endTimer();
    }
  };

  if (isLoading) {
    return <LoadingSkeleton />;
  }

  if (!session) {
    return null; // Will redirect to login
  }

  return (
    <div className="container flex items-center justify-center min-h-[calc(100vh-16rem)] py-12">
      <Card className="mx-auto max-w-sm w-full">
        <CardHeader className="space-y-1">
          <div className="flex justify-center mb-2">
            <Mail className="h-10 w-10 text-primary" />
          </div>
          <CardTitle className="text-2xl text-center">
            Check Your Email
          </CardTitle>
          <CardDescription className="text-center">
            We&apos;ve sent a verification link to your email address
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Resend feedback - only show when there's a message */}
          {resendMessage && (
            <Alert variant={resendSuccess ? "default" : "destructive"}>
              {resendSuccess ? (
                <CheckCircle className="h-4 w-4" />
              ) : (
                <AlertTriangle className="h-4 w-4" />
              )}
              <AlertDescription>{resendMessage}</AlertDescription>
            </Alert>
          )}
        </CardContent>

        <CardFooter>
          {/* Resend button with cooldown */}
          <Button
            onClick={handleResendVerification}
            disabled={isResending || resendCooldown > 0}
            className="w-full"
            variant="outline"
          >
            {isResending ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Sending...
              </>
            ) : resendCooldown > 0 ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4" />
                Resend in {resendCooldown}s
              </>
            ) : (
              <>
                <RefreshCw className="mr-2 h-4 w-4" />
                Resend Verification Email
              </>
            )}
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
