import { authOptions } from "@/lib/auth"
import { getServerSession as getNextAuthSession } from "next-auth"
import { getToken } from "next-auth/jwt"
import { cookies } from "next/headers"
import { NextResponse } from "next/server"

/**
 * Debug API to compare session data between NextAuth and custom sessions
 * This helps identify why OAuth and credential logins behave differently
 */
export async function GET(request: Request) {
  try {
    const cookieStore = await cookies()

    // Get NextAuth session
    const nextAuthSession = await getNextAuthSession(authOptions)

    // Get NextAuth JWT token
    const token = await getToken({
      req: request as any,
      secret: process.env.NEXTAUTH_SECRET
    })

    // Get custom session cookie
    const customSessionCookie = cookieStore.get("session")
    let customSession = null
    if (customSessionCookie) {
      try {
        customSession = JSON.parse(customSessionCookie.value)
      } catch {
        customSession = { error: "Failed to parse custom session" }
      }
    }

    // Get all cookies for debugging
    const allCookies = cookieStore.getAll()
    const cookieNames = allCookies.map(cookie => cookie.name)

    // Check for NextAuth cookies specifically
    const nextAuthCookies = allCookies.filter(cookie =>
      cookie.name.includes('next-auth') ||
      cookie.name.includes('__Secure-next-auth')
    )

    const debugInfo = {
      timestamp: new Date().toISOString(),
      nextAuthSession: nextAuthSession ? {
        user: nextAuthSession.user,
        expires: nextAuthSession.expires
      } : null,
      nextAuthToken: token ? {
        id: token.id,
        email: token.email,
        name: token.name,
        role: token.role,
        subscriptionPlan: token.subscriptionPlan,
        iat: token.iat,
        exp: token.exp,
        jti: token.jti
      } : null,
      customSession: customSession,
      cookieInfo: {
        totalCookies: allCookies.length,
        cookieNames: cookieNames,
        hasCustomSession: !!customSessionCookie,
        nextAuthCookies: nextAuthCookies.map(cookie => ({
          name: cookie.name,
          hasValue: !!cookie.value,
          valueLength: cookie.value?.length || 0
        }))
      },
      authenticationStatus: {
        hasNextAuthSession: !!nextAuthSession,
        hasNextAuthToken: !!token,
        hasCustomSession: !!customSession,
        sessionDataMatch: nextAuthSession && customSession ?
          nextAuthSession.user.id === customSession.user?.id : null
      }
    }

    return NextResponse.json(debugInfo, {
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    })

  } catch (error) {
    console.error("Session debug error:", error)
    return NextResponse.json({
      error: "Failed to get session debug info",
      details: error instanceof Error ? error.message : "Unknown error"
    }, { status: 500 })
  }
}
