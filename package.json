{"name": "sailor-plus", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "test": "jest", "test:unit": "jest __tests__/unit", "test:integration": "jest __tests__/integration", "test:performance": "jest __tests__/performance", "test:all": "pnpm test:unit && pnpm test:integration && pnpm test:performance", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "pnpm seed:test-user && pnpm seed:admin-user && playwright test", "test:e2e:production": "pnpm build && pnpm seed:test-user && pnpm seed:admin-user && playwright test", "test:e2e:ui": "pnpm seed:test-user && pnpm seed:admin-user && playwright test --ui", "test:e2e:manual": "node scripts/test-e2e-manual.js", "test:ci": "pnpm test:all && pnpm test:e2e", "seed:test-user": "node scripts/seed-test-user.js", "seed:admin-user": "node scripts/seed-admin-user.js", "seed:test-organizations": "node scripts/seed-test-organizations.js", "seed:notification-test": "node scripts/seed-notification-test-data.js"}, "dependencies": {"@auth/core": "latest", "@aws-sdk/client-rds-data": "latest", "@cloudflare/workers-types": "latest", "@electric-sql/pglite": "latest", "@hookform/resolvers": "^3.9.1", "@libsql/client": "latest", "@libsql/client-wasm": "latest", "@neondatabase/serverless": "^1.0.0", "@op-engineering/op-sqlite": "latest", "@opentelemetry/api": "latest", "@planetscale/database": "latest", "@prisma/client": "latest", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "latest", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "latest", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "@tidbcloud/serverless": "latest", "@types/better-sqlite3": "latest", "@types/jszip": "^3.4.1", "@types/pg": "latest", "@types/sql.js": "latest", "@uploadthing/react": "^7.3.1", "@vercel/postgres": "latest", "@xata.io/client": "latest", "autoprefixer": "^10.4.20", "bcrypt": "^6.0.0", "bcryptjs": "latest", "better-sqlite3": "latest", "bun-types": "latest", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "date-fns": "latest", "dotenv": "^16.5.0", "drizzle-orm": "latest", "embla-carousel-react": "8.5.1", "expo-sqlite": "latest", "gel": "latest", "input-otp": "1.4.1", "jszip": "^3.10.1", "knex": "latest", "kysely": "latest", "lucide-react": "^0.454.0", "mysql2": "latest", "nanoid": "latest", "next": "15.2.4", "next-auth": "latest", "next-themes": "latest", "nodemailer": "latest", "pg": "latest", "postgres": "latest", "react": "^19", "react-day-picker": "8.10.1", "react-dom": "^19", "react-hook-form": "^7.54.1", "react-resizable-panels": "^2.1.7", "recharts": "2.15.0", "resend": "^3.5.0", "sonner": "^1.7.1", "sql.js": "latest", "sqlite3": "latest", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "uploadthing": "^7.7.2", "vaul": "^0.9.6", "zod": "^3.24.1"}, "devDependencies": {"@playwright/test": "^1.52.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^14.3.1", "@testing-library/user-event": "^14.6.1", "@types/bcrypt": "^5.0.2", "@types/jest": "^29.5.14", "@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "cross-env": "^7.0.3", "eslint": "^9.29.0", "eslint-config-next": "15.3.3", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "playwright": "^1.52.0", "postcss": "^8", "tailwindcss": "^3.4.17", "typescript": "^5"}, "packageManager": "pnpm@10.11.1+sha512.e519b9f7639869dc8d5c3c5dfef73b3f091094b0a006d7317353c72b124e80e1afd429732e28705ad6bfa1ee879c1fce46c128ccebd3192101f43dd67c667912"}