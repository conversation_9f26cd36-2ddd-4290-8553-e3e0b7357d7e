#!/usr/bin/env node

/**
 * Seed Admin User Script
 * Creates a system administrator user for the Sailor Plus Maritime Platform
 *
 * Usage:
 *   node scripts/seed-admin-user.js
 *   pnpm seed:admin-user
 *
 * Environment Variables Required:
 *   DATABASE_URL - PostgreSQL connection string
 *
 * Production Deployment:
 *   - Run this script ONCE during initial production setup
 *   - Store admin credentials securely (password manager)
 *   - Consider using environment variables for admin email/password
 *   - Delete this script or restrict access after production deployment
 */

// Load environment variables
require("dotenv").config({ path: ".env.local" });

const { drizzle } = require("drizzle-orm/neon-http");
const { neon } = require("@neondatabase/serverless");
const { eq } = require("drizzle-orm");
const bcrypt = require("bcryptjs");
const { randomUUID } = require("crypto");
const { text, timestamp, boolean, pgTable } = require("drizzle-orm/pg-core");

// Define users table schema for this script
const users = pgTable("User", {
  id: text("id").primaryKey(),
  name: text("name").notNull(),
  email: text("email").notNull().unique(),
  password: text("password"),
  role: text("role").default("individual_user").notNull(),
  subscriptionPlan: text("subscriptionPlan")
    .default("individual_free")
    .notNull(),
  emailVerified: boolean("emailVerified").default(false).notNull(),
  createdAt: timestamp("createdAt").defaultNow().notNull(),
  updatedAt: timestamp("updatedAt").notNull(),
  lastLoginAt: timestamp("lastLoginAt"),
  deletedAt: timestamp("deletedAt"),
});

// Admin user configuration
const ADMIN_CONFIG = {
  email: process.env.ADMIN_EMAIL || "<EMAIL>",
  password: process.env.ADMIN_PASSWORD || "AdminSailorPlus2025!",
  name: process.env.ADMIN_NAME || "System Administrator",
};

async function seedAdminUser() {
  try {
    console.log("🔧 Seeding admin user for Sailor Plus Maritime Platform...");

    // Validate environment
    if (!process.env.DATABASE_URL) {
      throw new Error("DATABASE_URL environment variable is required");
    }

    // Prevent default credentials in production
    if (
      process.env.NODE_ENV === "production" &&
      (ADMIN_CONFIG.email === "<EMAIL>" ||
        ADMIN_CONFIG.password === "AdminSailorPlus2025!")
    ) {
      throw new Error(
        "Default admin credentials must not be used in production. Please set ADMIN_EMAIL and ADMIN_PASSWORD to secure values."
      );
    }

    // Initialize database connection
    const sql = neon(process.env.DATABASE_URL);
    const db = drizzle(sql);

    // Check if admin user already exists
    const existingAdmin = await db
      .select()
      .from(users)
      .where(eq(users.email, ADMIN_CONFIG.email))
      .limit(1);

    if (existingAdmin.length > 0) {
      const admin = existingAdmin[0];

      // Check if admin user has a password
      if (!admin.password) {
        console.log(
          `🔄 Updating existing admin user with password: ${ADMIN_CONFIG.email}`
        );

        // Hash password
        const hashedPassword = await bcrypt.hash(ADMIN_CONFIG.password, 12);

        // Update admin user with password and ensure email is verified
        await db
          .update(users)
          .set({
            password: hashedPassword,
            emailVerified: true,
            role: "system_admin",
            subscriptionPlan: "admin_unlimited",
            updatedAt: new Date(),
          })
          .where(eq(users.id, admin.id));

        console.log("✅ Admin user updated successfully with password!");
        console.log("");
        console.log("📋 Admin Credentials:");
        console.log(`   Email: ${ADMIN_CONFIG.email}`);
        console.log(`   Password: ${ADMIN_CONFIG.password}`);
        console.log(`   Role: system_admin`);
        console.log("");
        console.log("🌐 Access Admin Dashboard:");
        console.log("   URL: http://localhost:3000/admin");
        console.log("   Login: http://localhost:3000/login");
        return;
      } else {
        console.log(
          `⚠️  Admin user already exists with password: ${ADMIN_CONFIG.email}`
        );
        console.log("   Use the existing credentials or delete the user first");
        return;
      }
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(ADMIN_CONFIG.password, 12);

    // Create admin user
    const adminUser = {
      id: randomUUID(),
      email: ADMIN_CONFIG.email,
      name: ADMIN_CONFIG.name,
      password: hashedPassword, // Store hashed password in 'password' field
      role: "system_admin",
      subscriptionPlan: "admin_unlimited",
      emailVerified: true, // Admin users are pre-verified
      createdAt: new Date(),
      updatedAt: new Date(),
      lastLoginAt: null,
      deletedAt: null,
    };

    await db.insert(users).values(adminUser);

    console.log("✅ Admin user created successfully!");
    console.log("");
    console.log("📋 Admin Credentials:");
    console.log(`   Email: ${ADMIN_CONFIG.email}`);
    console.log(`   Password: ${ADMIN_CONFIG.password}`);
    console.log(`   Role: system_admin`);
    console.log("");
    console.log("🔐 Security Recommendations:");
    console.log(
      "   1. Change the default password immediately after first login"
    );
    console.log("   2. Store credentials in a secure password manager");
    console.log("   3. Enable 2FA when available");
    console.log("   4. Restrict access to this script in production");
    console.log("");
    console.log("🌐 Access Admin Dashboard:");
    console.log("   URL: http://localhost:3000/admin");
    console.log("   Login: http://localhost:3000/login");
  } catch (error) {
    console.error("❌ Error seeding admin user:", error.message);
    process.exit(1);
  }
}

// Production deployment guidance
function showProductionGuidance() {
  console.log("");
  console.log("🚀 PRODUCTION DEPLOYMENT GUIDANCE:");
  console.log("");
  console.log("1. ENVIRONMENT VARIABLES:");
  console.log("   Set these in your production environment:");
  console.log('   ADMIN_EMAIL="<EMAIL>"');
  console.log('   ADMIN_PASSWORD="SecureRandomPassword123!"');
  console.log('   ADMIN_NAME="Your Name"');
  console.log("");
  console.log("2. SECURITY CONSIDERATIONS:");
  console.log("   - Use a strong, unique password (20+ characters)");
  console.log("   - Use your company email domain");
  console.log("   - Run this script only once during initial setup");
  console.log("   - Delete or restrict access to this script after use");
  console.log("");
  console.log("3. DEPLOYMENT STEPS:");
  console.log("   a. Set environment variables in production");
  console.log("   b. Run: node scripts/seed-admin-user.js");
  console.log("   c. Verify admin login works");
  console.log("   d. Change password through admin interface");
  console.log("   e. Remove or secure this script");
  console.log("");
  console.log("4. BACKUP ADMIN ACCESS:");
  console.log("   Consider creating a second admin user as backup");
  console.log("   Store credentials in company password manager");
  console.log("");
}

// Main execution
if (process.argv.includes("--help") || process.argv.includes("-h")) {
  showProductionGuidance();
} else {
  seedAdminUser().then(() => {
    if (process.env.NODE_ENV === "production") {
      showProductionGuidance();
    }
  });
}
