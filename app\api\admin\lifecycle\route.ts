/**
 * Admin API: Organization Lifecycle Management
 * 
 * This endpoint provides admin access to organization lifecycle functions
 * for testing and management purposes.
 */

import {
  createAdminRecoveryRequest,
  createOrganizationLifecycleEvent,
  getAdminRecoveryRequests,
  getLatestGracePeriodEvent,
  getOrganizationGracePeriodDaysRemaining,
  getOrganizationLifecycleEvents,
  handleAdminAccountDeletion,
  isOrganizationInGracePeriod,
  reviewAdminRecoveryRequest
} from "@/lib/db";
import { NextRequest, NextResponse } from "next/server";

type TestResult = {
  name: string;
  status: string;
  result?: unknown;
  error?: string;
};

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const action = searchParams.get("action")
    const organizationId = searchParams.get("organizationId")

    switch (action) {
      case "test":
        // Test all lifecycle functions
        const testResults: {
          timestamp: string;
          tests: TestResult[];
        } = {
          timestamp: new Date().toISOString(),
          tests: []
        }

        // Test creating a lifecycle event
        try {
          const eventResult = await createOrganizationLifecycleEvent({
            organizationId: "test-org-lifecycle",
            eventType: "grace_period_started",
            triggeredBy: "test-admin",
            gracePeriodEnds: new Date(Date.now() + (30 * 24 * 60 * 60 * 1000)),
            metadata: { test: true, reason: "testing" }
          })
          testResults.tests.push({
            name: "createOrganizationLifecycleEvent",
            status: "success",
            result: eventResult
          })
        } catch (error) {
          testResults.tests.push({
            name: "createOrganizationLifecycleEvent",
            status: "error",
            error: error instanceof Error ? error.message : String(error)
          })
        }

        // Test getting lifecycle events
        try {
          const events = await getOrganizationLifecycleEvents("test-org-lifecycle")
          testResults.tests.push({
            name: "getOrganizationLifecycleEvents",
            status: "success",
            result: { eventCount: events.length, events: events.slice(0, 3) }
          })
        } catch (error) {
          testResults.tests.push({
            name: "getOrganizationLifecycleEvents",
            status: "error",
            error: error instanceof Error ? error.message : String(error)
          })
        }

        // Test grace period functions
        try {
          const inGracePeriod = await isOrganizationInGracePeriod("test-org-lifecycle")
          const daysRemaining = await getOrganizationGracePeriodDaysRemaining("test-org-lifecycle")
          testResults.tests.push({
            name: "gracePeriodFunctions",
            status: "success",
            result: { inGracePeriod, daysRemaining }
          })
        } catch (error) {
          testResults.tests.push({
            name: "gracePeriodFunctions",
            status: "error",
            error: error instanceof Error ? error.message : String(error)
          })
        }

        // Test admin recovery requests
        try {
          const recoveryResult = await createAdminRecoveryRequest({
            organizationId: "test-org-lifecycle",
            requestedBy: "test-user",
            requestReason: "Testing admin recovery request functionality"
          })
          testResults.tests.push({
            name: "createAdminRecoveryRequest",
            status: "success",
            result: recoveryResult
          })
        } catch (error) {
          testResults.tests.push({
            name: "createAdminRecoveryRequest",
            status: "error",
            error: error instanceof Error ? error.message : String(error)
          })
        }

        return NextResponse.json({
          success: true,
          message: "Lifecycle functions test completed",
          data: testResults
        })

      case "events":
        if (!organizationId) {
          return NextResponse.json({
            success: false,
            error: "organizationId parameter required"
          }, { status: 400 })
        }

        const events = await getOrganizationLifecycleEvents(organizationId)
        return NextResponse.json({
          success: true,
          data: { organizationId, events }
        })

      case "grace-period":
        if (!organizationId) {
          return NextResponse.json({
            success: false,
            error: "organizationId parameter required"
          }, { status: 400 })
        }

        const inGracePeriod = await isOrganizationInGracePeriod(organizationId)
        const daysRemaining = await getOrganizationGracePeriodDaysRemaining(organizationId)
        const latestEvent = await getLatestGracePeriodEvent(organizationId)

        return NextResponse.json({
          success: true,
          data: {
            organizationId,
            inGracePeriod,
            daysRemaining,
            latestEvent
          }
        })

      case "recovery-requests":
        const requests = await getAdminRecoveryRequests("pending")
        return NextResponse.json({
          success: true,
          data: { requests }
        })

      default:
        return NextResponse.json({
          success: false,
          error: "Invalid action. Available actions: test, events, grace-period, recovery-requests"
        }, { status: 400 })
    }

  } catch (error) {
    console.error("Admin lifecycle API error:", error)
    return NextResponse.json({
      success: false,
      error: "Internal server error",
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { action } = body

    switch (action) {
      case "handle-admin-deletion":
        const { organizationId, deletedAdminUserId } = body
        if (!organizationId || !deletedAdminUserId) {
          return NextResponse.json({
            success: false,
            error: "organizationId and deletedAdminUserId required"
          }, { status: 400 })
        }

        const result = await handleAdminAccountDeletion(organizationId, deletedAdminUserId)
        return NextResponse.json({
          success: true,
          message: "Admin deletion handled successfully",
          data: result
        })

      case "review-recovery-request":
        const { requestId, reviewedBy, status, reviewNotes } = body
        if (!requestId || !reviewedBy || !status) {
          return NextResponse.json({
            success: false,
            error: "requestId, reviewedBy, and status required"
          }, { status: 400 })
        }

        const reviewResult = await reviewAdminRecoveryRequest(requestId, reviewedBy, status, reviewNotes)
        return NextResponse.json({
          success: true,
          message: "Recovery request reviewed successfully",
          data: reviewResult
        })

      default:
        return NextResponse.json({
          success: false,
          error: "Invalid action. Available actions: handle-admin-deletion, review-recovery-request"
        }, { status: 400 })
    }

  } catch (error) {
    console.error("Admin lifecycle API error:", error)
    return NextResponse.json({
      success: false,
      error: "Internal server error",
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 })
  }
}
