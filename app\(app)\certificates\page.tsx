"use client";

import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ArrowUpDown,
  Clock,
  FileText,
  Filter,
  LayoutGrid,
  Plus,
  Search,
  Star,
  Table2,
} from "lucide-react";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import { useCallback, useEffect, useMemo, useState } from "react";

import { CertificateBulkActions } from "@/components/certificate-bulk-actions";
import { CertificateCard } from "@/components/certificate-card";
import { CertificateTable } from "@/components/certificate-table";
import { CertificatesPageSkeleton } from "@/components/certificates-skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { Ta<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  CertificatesProvider,
  useCertificates,
  useFilteredCertificates,
  type FilterType,
  type SortField,
  type SortOrder,
} from "@/contexts/certificates-context";
import { useToast } from "@/hooks/use-toast";

function CertificatesPageContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { toast } = useToast();

  // URL-based state management
  const query = searchParams?.get("query") || "";
  const urlView = searchParams?.get("view") || "grid";
  const urlFilter = searchParams?.get("filter") || "all";
  const urlSort = searchParams?.get("sort") || "expiryDate";
  const urlOrder = searchParams?.get("order") || "asc";

  // Component state for UI controls
  const [view, setView] = useState<"grid" | "table">(
    urlView as "grid" | "table"
  );
  const [activeFilter, setActiveFilter] = useState<FilterType>(
    urlFilter as FilterType
  );
  const [searchQuery, setSearchQuery] = useState(query);
  const [sortBy, setSortBy] = useState<SortField>(urlSort as SortField);
  const [sortOrder, setSortOrder] = useState<SortOrder>(urlOrder as SortOrder);

  // Bulk operations state
  const [selectedCertificates, setSelectedCertificates] = useState<string[]>(
    []
  );

  // Use the custom certificates hook for data management
  const { certificates, isLoading, error, deleteCertificate, toggleFavorite } =
    useCertificates();

  // Use client-side filtering and sorting (enhanced to include tags in search)
  const { filteredCertificates, counts } = useFilteredCertificates(
    certificates,
    searchQuery,
    activeFilter,
    sortBy,
    sortOrder
  );

  // URL state synchronization
  const updateURL = useCallback(
    (params: Record<string, string>) => {
      const newSearchParams = new URLSearchParams(searchParams?.toString());
      Object.entries(params).forEach(([key, value]) => {
        if (value) {
          newSearchParams.set(key, value);
        } else {
          newSearchParams.delete(key);
        }
      });
      router.push(`/certificates?${newSearchParams.toString()}`, {
        scroll: false,
      });
    },
    [router, searchParams]
  );

  // Debounce utility function
  function debounce(
    func: (query: string) => void,
    wait: number
  ): (query: string) => void {
    let timeout: NodeJS.Timeout;
    return (query: string) => {
      clearTimeout(timeout);
      timeout = setTimeout(() => func(query), wait);
    };
  }

  // Debounced search - now updates local state immediately for instant feedback
  const debouncedUpdateURL = useCallback(
    debounce((query: string) => {
      updateURL({ query: query || "" });
    }, 300),
    [updateURL]
  );

  const handleSearchChange = useCallback(
    (value: string) => {
      setSearchQuery(value); // Immediate local state update
      debouncedUpdateURL(value); // Debounced URL update
    },
    [debouncedUpdateURL]
  );

  const handleViewCertificate = useCallback(
    (certificateId: string) => {
      router.push(`/certificates/${certificateId}`);
    },
    [router]
  );

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Ctrl/Cmd + N to add new certificate
      if ((event.ctrlKey || event.metaKey) && event.key === "n") {
        event.preventDefault();
        router.push("/certificates/new");
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, [router]);

  // Memoized action card handlers to prevent unnecessary re-renders
  const handleFilterAll = useCallback(() => {
    setActiveFilter("all");
    updateURL({ filter: "all" });
  }, [updateURL]);

  const handleFilterFavorites = useCallback(() => {
    setActiveFilter("favorites");
    updateURL({ filter: "favorites" });
  }, [updateURL]);

  const handleFilterExpiringSoon = useCallback(() => {
    setActiveFilter("expiring-soon");
    updateURL({ filter: "expiring-soon" });
  }, [updateURL]);

  const handleFilterExpired = useCallback(() => {
    setActiveFilter("expired");
    updateURL({ filter: "expired" });
  }, [updateURL]);

  const actionCards = useMemo(
    () => [
      {
        title: "All",
        description: "View all certificates",
        icon: FileText,
        count: counts.all,
        onClick: handleFilterAll,
        variant: "outline" as const,
      },
      {
        title: "Favorites",
        description: "View your favorite certificates",
        icon: Star,
        count: counts.favorites,
        onClick: handleFilterFavorites,
        variant: "outline" as const,
      },
      {
        title: "Expiring Soon",
        description: "Certificates that need attention",
        icon: Clock,
        count: counts.expiringSoon,
        onClick: handleFilterExpiringSoon,
        variant: "outline" as const,
      },
      {
        title: "Expired",
        description: "View expired certificates",
        icon: AlertCircle,
        count: counts.expired,
        onClick: handleFilterExpired,
        variant: "outline" as const,
      },
    ],
    [
      counts,
      handleFilterAll,
      handleFilterFavorites,
      handleFilterExpiringSoon,
      handleFilterExpired,
    ]
  );

  // Memoized handlers for sort and view changes
  const handleSortByChange = useCallback(
    (value: string) => {
      setSortBy(value as SortField);
      updateURL({ sort: value });
    },
    [updateURL]
  );

  const handleSortOrderChange = useCallback(
    (value: string) => {
      setSortOrder(value as SortOrder);
      updateURL({ order: value });
    },
    [updateURL]
  );

  const handleViewChange = useCallback(
    (v: string) => {
      setView(v as "grid" | "table");
      updateURL({ view: v });
    },
    [updateURL]
  );

  const handleClearFilters = useCallback(() => {
    setActiveFilter("all");
    setSearchQuery("");
    updateURL({ filter: "all", query: "" });
  }, [updateURL]);

  // Bulk operations handlers
  const handleBulkDelete = useCallback(
    async (ids: string[]) => {
      try {
        // Delete certificates one by one (could be optimized with batch API)
        await Promise.all(ids.map((id) => deleteCertificate(id)));
        setSelectedCertificates([]);
        toast({
          title: "Success",
          description: `Successfully deleted ${ids.length} certificate${
            ids.length > 1 ? "s" : ""
          }`,
          "data-testid": "bulk-delete-success-toast",
        } as any);
      } catch (error) {
        console.error("Bulk delete error:", error);
        toast({
          title: "Error",
          description: "Failed to delete certificates",
          variant: "destructive",
        });
      }
    },
    [deleteCertificate, toast]
  );

  const handleBulkFavorite = useCallback(
    async (ids: string[], isFavorite: boolean) => {
      try {
        // Filter certificates that need to be toggled
        const certificatesToToggle = certificates.filter(
          (cert) => ids.includes(cert.id) && cert.isFavorite !== isFavorite
        );

        // Toggle only the ones that need to change
        await Promise.all(
          certificatesToToggle.map((cert) => toggleFavorite(cert.id))
        );

        toast({
          title: "Success",
          description: `Successfully ${
            isFavorite ? "added to" : "removed from"
          } favorites`,
          "data-testid": "bulk-favorite-success-toast",
        } as any);
      } catch (error) {
        console.error("Bulk favorite error:", error);
        toast({
          title: "Error",
          description: "Failed to update favorites",
          variant: "destructive",
        });
      }
    },
    [certificates, toggleFavorite, toast]
  );

  const handleBulkDownload = useCallback(
    async (_ids: string[]) => {
      try {
        // This would need a bulk download API endpoint
        // For now, we'll show a placeholder
        toast({
          title: "Feature Coming Soon",
          description: "Bulk download will be available soon",
          "data-testid": "bulk-download-coming-soon-toast",
        } as any);
      } catch (error) {
        console.error("Bulk download error:", error);
        toast({
          title: "Error",
          description: "Failed to download certificates",
          variant: "destructive",
        });
      }
    },
    [toast]
  );

  const handleSelectAll = useCallback(() => {
    setSelectedCertificates(filteredCertificates.map((cert) => cert.id));
  }, [filteredCertificates]);

  const handleDeselectAll = useCallback(() => {
    setSelectedCertificates([]);
  }, []);

  if (isLoading) {
    return (
      <div className="space-y-4 sm:space-y-6 p-4 sm:p-6 md:p-8">
        <CertificatesPageSkeleton />
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-4 sm:space-y-6 p-4 sm:p-6 md:p-8">
        <div className="flex flex-col gap-4">
          <div>
            <h1 className="text-xl sm:text-2xl font-bold tracking-tight">
              Certificates
            </h1>
            <p className="text-sm sm:text-base text-muted-foreground">
              Manage your professional certificates and documents
            </p>
          </div>
        </div>
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
        <div className="text-center">
          <Button onClick={() => window.location.reload()}>Try Again</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4 sm:space-y-6 p-4 sm:p-6 md:p-8">
      {/* Header Section */}
      <div className="flex flex-col gap-4">
        <div>
          <h1 className="text-xl sm:text-2xl font-bold tracking-tight">
            Certificates
          </h1>
          <p className="text-sm sm:text-base text-muted-foreground">
            Manage your professional certificates and documents
          </p>
        </div>

        {/* Enhanced search and filtering */}
        <div className="space-y-3">
          {/* Primary search row */}
          <div className="flex flex-col sm:flex-row gap-3 sm:gap-2">
            {/* Search - Full width on mobile */}
            <div className="relative flex-1 sm:max-w-sm">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search certificates..."
                className="w-full pl-8 h-10"
                value={searchQuery}
                onChange={(e) => handleSearchChange(e.target.value)}
                data-testid="certificates-search"
              />
            </div>

            {/* Action buttons */}
            <div className="flex gap-2 sm:gap-2">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="outline"
                    size="icon"
                    className="h-11 w-11 flex-shrink-0"
                    data-testid="filter-button"
                  >
                    <Filter className="h-4 w-4" />
                    <span className="sr-only">Filter options</span>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-56">
                  <DropdownMenuLabel>Filter by</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuRadioGroup
                    value={activeFilter}
                    onValueChange={(value) => {
                      setActiveFilter(value as FilterType);
                      updateURL({ filter: value });
                    }}
                  >
                    <DropdownMenuRadioItem value="all">
                      All Certificates
                    </DropdownMenuRadioItem>
                    <DropdownMenuRadioItem value="favorites">
                      Favorites
                    </DropdownMenuRadioItem>
                    <DropdownMenuRadioItem value="expiring-soon">
                      Expiring Soon
                    </DropdownMenuRadioItem>
                    <DropdownMenuRadioItem value="expired">
                      Expired
                    </DropdownMenuRadioItem>
                  </DropdownMenuRadioGroup>
                </DropdownMenuContent>
              </DropdownMenu>

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="outline"
                    className="h-11 flex-shrink-0 gap-2 px-3"
                    data-testid="sort-button"
                  >
                    <ArrowUpDown className="h-4 w-4" />
                    <span className="hidden sm:inline">
                      {sortBy === "name"
                        ? "Name"
                        : sortBy === "dateIssued"
                        ? "Date Issued"
                        : sortBy === "expiryDate"
                        ? "Expiry Date"
                        : "Sort"}
                    </span>
                    <span className="sm:hidden">Sort</span>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-56">
                  <DropdownMenuLabel>Sort by</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuRadioGroup
                    value={sortBy}
                    onValueChange={handleSortByChange}
                  >
                    <DropdownMenuRadioItem value="name">
                      Name
                    </DropdownMenuRadioItem>
                    <DropdownMenuRadioItem value="dateIssued">
                      Date Issued
                    </DropdownMenuRadioItem>
                    <DropdownMenuRadioItem value="expiryDate">
                      Expiry Date
                    </DropdownMenuRadioItem>
                  </DropdownMenuRadioGroup>
                  <DropdownMenuSeparator />
                  <DropdownMenuRadioGroup
                    value={sortOrder}
                    onValueChange={handleSortOrderChange}
                  >
                    <DropdownMenuRadioItem value="asc">
                      Ascending
                    </DropdownMenuRadioItem>
                    <DropdownMenuRadioItem value="desc">
                      Descending
                    </DropdownMenuRadioItem>
                  </DropdownMenuRadioGroup>
                </DropdownMenuContent>
              </DropdownMenu>

              <Button
                asChild
                className="h-11 flex-1 sm:flex-none"
                data-testid="add-certificate-button"
              >
                <Link href="/certificates/new">
                  <Plus className="h-4 w-4 mr-2" />
                  <span className="hidden sm:inline">Add Certificate</span>
                  <span className="sm:hidden">Add</span>
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Action Cards - Improved mobile layout */}
      <div className="grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
        {actionCards.map((card, index) => (
          <Card
            key={index}
            className={`cursor-pointer transition-all hover:shadow-md active:scale-95 ${
              (activeFilter === "all" && card.title === "All") ||
              (activeFilter === "favorites" && card.title === "Favorites") ||
              (activeFilter === "expiring-soon" &&
                card.title === "Expiring Soon") ||
              (activeFilter === "expired" && card.title === "Expired")
                ? "ring-2 ring-primary bg-primary/5"
                : ""
            }`}
            onClick={card.onClick}
            data-testid={`filter-${card.title
              .toLowerCase()
              .replace(/\s+/g, "-")}`}
          >
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 p-4 sm:p-6">
              <CardTitle className="text-xs sm:text-sm font-medium leading-tight">
                {card.title}
              </CardTitle>
              <card.icon className="h-4 w-4 text-muted-foreground flex-shrink-0" />
            </CardHeader>
            <CardContent className="p-4 sm:p-6 pt-0">
              <div className="text-xl sm:text-2xl font-bold">
                {card.count !== undefined ? card.count : ""}
              </div>
              <p className="text-xs text-muted-foreground leading-tight">
                {card.description}
              </p>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Bulk Actions */}
      {selectedCertificates.length > 0 && (
        <CertificateBulkActions
          selectedCertificates={certificates.filter((cert) =>
            selectedCertificates.includes(cert.id)
          )}
          onSelectAll={handleSelectAll}
          onDeselectAll={handleDeselectAll}
          onBulkDelete={handleBulkDelete}
          onBulkFavorite={handleBulkFavorite}
          onBulkDownload={handleBulkDownload}
          totalCertificates={filteredCertificates.length}
        />
      )}

      {/* Certificates List */}
      <Card>
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center p-4 sm:p-6 pb-0">
          <div className="flex items-center space-x-2 w-full sm:w-auto">
            <Tabs
              value={view}
              onValueChange={handleViewChange}
              className="w-full sm:w-auto"
            >
              <TabsList className="grid w-full grid-cols-2 sm:w-auto">
                <TabsTrigger value="grid" className="text-sm">
                  <LayoutGrid className="h-4 w-4" />
                  <span className="sr-only">Grid view</span>
                </TabsTrigger>
                <TabsTrigger value="table" className="text-sm">
                  <Table2 className="h-4 w-4" />
                  <span className="sr-only">Table view</span>
                </TabsTrigger>
              </TabsList>
            </Tabs>
          </div>
        </div>

        <div className="p-4 sm:p-6">
          {view === "table" ? (
            <div className="overflow-x-auto">
              <CertificateTable
                certificates={filteredCertificates}
                onView={handleViewCertificate}
                selectedCertificates={selectedCertificates}
                onSelectionChange={setSelectedCertificates}
                enableSelection={true}
              />
            </div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredCertificates.map((cert) => (
                <CertificateCard
                  key={cert.id}
                  cert={cert}
                  onView={handleViewCertificate}
                  isSelected={selectedCertificates.includes(cert.id)}
                  onSelectionChange={(id, selected) => {
                    if (selected) {
                      setSelectedCertificates((prev) => [...prev, id]);
                    } else {
                      setSelectedCertificates((prev) =>
                        prev.filter((certId) => certId !== id)
                      );
                    }
                  }}
                />
              ))}
            </div>
          )}

          {filteredCertificates.length === 0 && (
            <div className="flex flex-col items-center justify-center py-8 sm:py-12 text-center">
              <FileText className="h-10 w-10 sm:h-12 sm:w-12 text-muted-foreground mb-4" />
              <h3 className="text-base sm:text-lg font-medium">
                No certificates found
              </h3>
              <p className="text-sm text-muted-foreground mb-4 max-w-sm">
                {activeFilter === "all"
                  ? "Get started by adding a new certificate."
                  : "No certificates match the selected filter."}
              </p>
              {(activeFilter !== "all" || searchQuery) && (
                <Button
                  variant="outline"
                  onClick={handleClearFilters}
                  className="h-10"
                >
                  Clear all filters
                </Button>
              )}
            </div>
          )}
        </div>
      </Card>
    </div>
  );
}

export default function CertificatesPage() {
  return (
    <CertificatesProvider>
      <CertificatesPageContent />
    </CertificatesProvider>
  );
}
