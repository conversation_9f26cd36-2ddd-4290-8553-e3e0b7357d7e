/**
 * Enhanced authentication configuration for route protection
 * Designed to support RBAC and multi-tenant functionality
 */

// User roles for RBAC
// Note: Organizations (cert providers, yacht companies) are NOT user roles
// Users can be members/admins of organizations via OrganizationMembership table
export enum UserRole {
  INDIVIDUAL_USER = "individual_user", // All end users (maritime professionals, org admins, etc.)
  SYSTEM_ADMIN = "system_admin"        // Internal company staff only
}

// Legacy role mapping for backward compatibility (session migration only)
// Note: This is only used for migrating existing sessions, not for new users
export const LEGACY_ROLE_MAPPING = {
  "legacy_maritime_user": UserRole.INDIVIDUAL_USER // Legacy role mapping
} as const

// SCAFFOLDING: Individual user subscription plans (for maritime professionals)
// Note: These are placeholder enums for future subscription system implementation
// Currently not used in business logic - kept for session compatibility
export enum IndividualSubscriptionPlan {
  FREE = "individual_free",
  BASIC = "individual_basic",
  PREMIUM = "individual_premium",
  PROFESSIONAL = "individual_professional"
}

// SCAFFOLDING: Organization subscription plans (for future B2B features)
// Note: These are placeholders - organizations don't currently have subscription plans
// Organization features are controlled via verification status and feature gates
export enum ProviderSubscriptionPlan {
  STARTER = "provider_starter",
  PROFESSIONAL = "provider_professional",
  ENTERPRISE = "provider_enterprise"
}

export enum CompanySubscriptionPlan {
  BASIC = "company_basic",
  PROFESSIONAL = "company_professional",
  ENTERPRISE = "company_enterprise",
  FLEET = "company_fleet"
}

// SCAFFOLDING: Unified subscription plan type for session context
// Note: Currently only IndividualSubscriptionPlan is used in practice
export type SubscriptionPlan =
  | IndividualSubscriptionPlan
  | ProviderSubscriptionPlan
  | CompanySubscriptionPlan
  | "system_admin" // System admins don't have subscription plans

// SCAFFOLDING: Permissions for future granular access control
// Note: Currently simplified - only basic role-based access is implemented
// These permissions are placeholders for future RBAC implementation
export enum Permission {
  // Basic certificate permissions (currently used)
  CERTIFICATES_READ = "certificates:read",
  CERTIFICATES_WRITE = "certificates:write",
  CERTIFICATES_DELETE = "certificates:delete",
  CERTIFICATES_BULK_OPERATIONS = "certificates:bulk",

  // Future: User management permissions (not implemented)
  USERS_READ = "users:read",
  USERS_WRITE = "users:write",
  USERS_DELETE = "users:delete",

  // Future: Organization management permissions (not tenant-based)
  // Note: Organization permissions handled via OrganizationMembership, not tenant system
  TENANT_ADMIN = "tenant:admin", // Legacy - will be replaced with organization-based permissions
  TENANT_READ = "tenant:read",   // Legacy - will be replaced with organization-based permissions

  // System permissions (used for admin routes)
  SYSTEM_ADMIN = "system:admin",
  ANALYTICS_READ = "analytics:read",

  // Future: Premium features (not implemented)
  ADVANCED_REPORTING = "features:advanced_reporting",
  API_ACCESS = "features:api_access",
  BULK_EXPORT = "features:bulk_export"
}

// Session context for authentication and authorization
// Note: Organization context is handled via OrganizationMembership table, not session fields
export interface SessionContext {
  userId: string
  email: string
  name: string
  role: UserRole
  subscriptionPlan: SubscriptionPlan
  permissions: Permission[]
  emailVerified: boolean // Email verification status
  // Note: Organization membership and roles are handled via OrganizationMembership table
  // Users can belong to multiple organizations with different roles per organization
}

// Enhanced route configuration
export interface RouteConfig {
  pattern: string | RegExp
  requiresAuth: boolean
  allowedRoles?: UserRole[]
  requiredPermissions?: Permission[]
  minimumPlan?: SubscriptionPlan
  tenantRequired?: boolean
  redirectTo?: string
  description?: string
  // Future: rate limiting, feature flags, etc.
  rateLimit?: {
    requests: number
    windowMs: number
  }
}

/**
 * Route configuration for the application
 * Routes are checked in order - first match wins
 */
export const routeConfig: RouteConfig[] = [
  // Public routes (no authentication required)
  {
    pattern: "/",
    requiresAuth: false,
    description: "Landing page"
  },
  {
    pattern: /^\/login/,
    requiresAuth: false,
    description: "Login pages"
  },
  {
    pattern: /^\/signup/,
    requiresAuth: false,
    description: "Signup pages"
  },
  {
    pattern: /^\/auth\//,
    requiresAuth: false,
    description: "Auth callback routes"
  },
  {
    pattern: /^\/api\/auth\//,
    requiresAuth: false,
    description: "Auth API routes"
  },
  {
    pattern: /^\/verify-email/,
    requiresAuth: false,
    description: "Email verification page"
  },
  {
    pattern: /^\/verification-pending/,
    requiresAuth: false,
    description: "Email verification pending page"
  },
  {
    pattern: /^\/forgot-password/,
    requiresAuth: false,
    description: "Forgot password page"
  },
  {
    pattern: /^\/reset-password/,
    requiresAuth: false,
    description: "Reset password page"
  },

  // Basic authenticated routes (all user types)
  {
    pattern: /^\/dashboard/,
    requiresAuth: true,
    description: "Dashboard pages"
  },
  {
    pattern: /^\/certificates/,
    requiresAuth: true,
    // Note: Permission checks are scaffolding - currently all authenticated users have access
    description: "Certificate management"
  },
  {
    pattern: /^\/profile/,
    requiresAuth: true,
    description: "User profile"
  },
  {
    pattern: /^\/settings/,
    requiresAuth: true,
    description: "Application settings"
  },
  {
    pattern: /^\/notifications/,
    requiresAuth: true,
    description: "Notifications"
  },
  {
    pattern: /^\/help/,
    requiresAuth: true,
    description: "Help pages"
  },

  // SCAFFOLDING: Premium features (not implemented)
  {
    pattern: /^\/analytics/,
    requiresAuth: true,
    // Note: Permission and subscription checks are scaffolding - not implemented
    // requiredPermissions: [Permission.ANALYTICS_READ],
    // minimumPlan: IndividualSubscriptionPlan.PREMIUM,
    description: "Analytics dashboard (future feature)"
  },

  // Note: Organization admin routes will be handled via organization context switching
  // within the main dashboard, not separate admin routes

  // System admin routes (Phase 5 implementation)
  {
    pattern: /^\/admin/,
    requiresAuth: true,
    allowedRoles: [UserRole.SYSTEM_ADMIN], // This is actively used in middleware
    // Note: Permission check is scaffolding - role check is sufficient
    description: "Admin dashboard and system administration"
  },

  // Default: require authentication for all other routes
  {
    pattern: /.*/,
    requiresAuth: true,
    description: "Default protection for all other routes"
  }
]

/**
 * Check if a route requires authentication and validate permissions
 */
export function getRouteConfig(pathname: string): RouteConfig | null {
  for (const config of routeConfig) {
    if (typeof config.pattern === 'string') {
      if (pathname === config.pattern) {
        return config
      }
    } else {
      if (config.pattern.test(pathname)) {
        return config
      }
    }
  }
  return null
}

/**
 * Check if user has required permissions for a route
 * Currently simplified - will be enhanced when RBAC is implemented
 */
export function hasRouteAccess(
  routeConfig: RouteConfig,
  sessionContext?: SessionContext
): boolean {
  // Basic auth check
  if (routeConfig.requiresAuth && !sessionContext) {
    return false
  }

  // For now, if user is authenticated, they have access
  // Future: implement role, permission, and subscription checks
  if (sessionContext) {
    // TODO: Implement when RBAC is added
    // - Check allowedRoles
    // - Check requiredPermissions
    // - Check minimumPlan
    // - Check tenantRequired
    return true
  }

  return !routeConfig.requiresAuth
}

/**
 * Get default redirect URL based on user role and context
 * Implements role-based redirects for proper user experience
 */
export function getDefaultRedirect(sessionContext?: SessionContext): string {
  if (!sessionContext) {
    return authUrls.login
  }

  // Role-based default redirects
  switch (sessionContext.role) {
    case UserRole.SYSTEM_ADMIN:
      return '/dashboard' // System admins go to dashboard first, can navigate to /admin
    case UserRole.INDIVIDUAL_USER:
    default:
      return '/dashboard' // All users go to main dashboard
  }
}

/**
 * Auth redirect URLs
 */
export const authUrls = {
  login: "/login",
  signIn: "/login",
  dashboard: "/dashboard",
  afterSignIn: "/dashboard",
  defaultRedirect: "/dashboard",
  verifyEmail: "/verify-email",
  verificationPending: "/verification-pending",
  // Future: role-specific defaults
  adminProvider: "/admin/provider",
  adminCompany: "/admin/company",
  adminSystem: "/admin/system"
} as const

/**
 * Default permissions by role
 * Will be used when implementing RBAC
 * Note: Organization-specific permissions are handled via OrganizationMembership roles
 */
export const defaultPermissionsByRole: Record<UserRole, Permission[]> = {
  [UserRole.INDIVIDUAL_USER]: [
    Permission.CERTIFICATES_READ,
    Permission.CERTIFICATES_WRITE,
    Permission.CERTIFICATES_DELETE
  ],
  [UserRole.SYSTEM_ADMIN]: [
    ...Object.values(Permission) // System admins have all permissions
  ]
}

/**
 * SCAFFOLDING: Individual user subscription plan features
 * Note: Currently all individual users have the same basic permissions regardless of plan
 * This mapping is kept for future subscription system implementation
 * For maritime professionals (yachting, commercial shipping, offshore, cruise, recreational boating, etc.)
 */
export const individualPlanFeatures: Record<IndividualSubscriptionPlan, Permission[]> = {
  [IndividualSubscriptionPlan.FREE]: [
    Permission.CERTIFICATES_READ,
    Permission.CERTIFICATES_WRITE,
    Permission.CERTIFICATES_DELETE // Currently all users can delete their own certificates
  ],
  [IndividualSubscriptionPlan.BASIC]: [
    Permission.CERTIFICATES_READ,
    Permission.CERTIFICATES_WRITE,
    Permission.CERTIFICATES_DELETE
  ],
  [IndividualSubscriptionPlan.PREMIUM]: [
    Permission.CERTIFICATES_READ,
    Permission.CERTIFICATES_WRITE,
    Permission.CERTIFICATES_DELETE,
    Permission.CERTIFICATES_BULK_OPERATIONS,
    Permission.ANALYTICS_READ,
    Permission.ADVANCED_REPORTING,
    Permission.BULK_EXPORT
  ],
  [IndividualSubscriptionPlan.PROFESSIONAL]: [
    Permission.CERTIFICATES_READ,
    Permission.CERTIFICATES_WRITE,
    Permission.CERTIFICATES_DELETE,
    Permission.CERTIFICATES_BULK_OPERATIONS,
    Permission.ANALYTICS_READ,
    Permission.ADVANCED_REPORTING,
    Permission.BULK_EXPORT,
    Permission.API_ACCESS
  ]
}

/**
 * SCAFFOLDING: Certification provider subscription plan features
 * Note: Organizations don't currently have subscription plans
 * Organization features are controlled via verification status and feature gates
 * This mapping is kept for future B2B subscription system implementation
 */
export const providerPlanFeatures: Record<ProviderSubscriptionPlan, Permission[]> = {
  [ProviderSubscriptionPlan.STARTER]: [
    Permission.CERTIFICATES_READ,
    Permission.CERTIFICATES_WRITE,
    Permission.USERS_READ,
    Permission.TENANT_READ // Legacy - will be replaced with organization-based permissions
  ],
  [ProviderSubscriptionPlan.PROFESSIONAL]: [
    Permission.CERTIFICATES_READ,
    Permission.CERTIFICATES_WRITE,
    Permission.CERTIFICATES_BULK_OPERATIONS,
    Permission.USERS_READ,
    Permission.USERS_WRITE,
    Permission.TENANT_ADMIN, // Legacy - will be replaced with organization-based permissions
    Permission.ANALYTICS_READ
  ],
  [ProviderSubscriptionPlan.ENTERPRISE]: [
    Permission.CERTIFICATES_READ,
    Permission.CERTIFICATES_WRITE,
    Permission.CERTIFICATES_BULK_OPERATIONS,
    Permission.USERS_READ,
    Permission.USERS_WRITE,
    Permission.TENANT_ADMIN, // Legacy - will be replaced with organization-based permissions
    Permission.ANALYTICS_READ,
    Permission.ADVANCED_REPORTING,
    Permission.API_ACCESS
  ]
}

/**
 * SCAFFOLDING: Yacht company subscription plan features
 * Note: Organizations don't currently have subscription plans
 * Organization features are controlled via verification status and feature gates
 * This mapping is kept for future B2B subscription system implementation
 */
export const companyPlanFeatures: Record<CompanySubscriptionPlan, Permission[]> = {
  [CompanySubscriptionPlan.BASIC]: [
    Permission.CERTIFICATES_READ,
    Permission.USERS_READ,
    Permission.TENANT_READ // Legacy - will be replaced with organization-based permissions
  ],
  [CompanySubscriptionPlan.PROFESSIONAL]: [
    Permission.CERTIFICATES_READ,
    Permission.USERS_READ,
    Permission.TENANT_READ, // Legacy - will be replaced with organization-based permissions
    Permission.ANALYTICS_READ
  ],
  [CompanySubscriptionPlan.ENTERPRISE]: [
    Permission.CERTIFICATES_READ,
    Permission.USERS_READ,
    Permission.TENANT_READ, // Legacy - will be replaced with organization-based permissions
    Permission.ANALYTICS_READ,
    Permission.ADVANCED_REPORTING
  ],
  [CompanySubscriptionPlan.FLEET]: [
    Permission.CERTIFICATES_READ,
    Permission.USERS_READ,
    Permission.TENANT_READ, // Legacy - will be replaced with organization-based permissions
    Permission.ANALYTICS_READ,
    Permission.ADVANCED_REPORTING,
    Permission.BULK_EXPORT,
    Permission.API_ACCESS
  ]
}
